import { g as getCollection } from "./_astro_content.Cyc5QpB4.js";
class TagManager {
  tagCache = /* @__PURE__ */ new Map();
  lastUpdate = 0;
  cacheTimeout = 5 * 60 * 1e3;
  // 5分钟缓存
  /**
   * 获取所有标签统计信息
   */
  async getTagStats() {
    const now = Date.now();
    if (now - this.lastUpdate < this.cacheTimeout && this.tagCache.size > 0) {
      return this.buildStatsFromCache();
    }
    await this.refreshTagCache();
    return this.buildStatsFromCache();
  }
  /**
   * 刷新标签缓存
   */
  async refreshTagCache() {
    this.tagCache.clear();
    const collections = [
      "research",
      "news",
      "logs",
      "ai",
      "economics",
      "philosophy",
      "internet",
      "future",
      "products",
      "reflections"
    ];
    const tagCounts = /* @__PURE__ */ new Map();
    const tagCategories = /* @__PURE__ */ new Map();
    const tagRelations = /* @__PURE__ */ new Map();
    for (const collectionName of collections) {
      try {
        const collection = await getCollection(collectionName);
        for (const entry of collection) {
          const tags = entry.data.tags || [];
          for (const tag of tags) {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
            if (!tagCategories.has(tag)) {
              tagCategories.set(tag, this.categorizeTag(tag, collectionName));
            }
            if (!tagRelations.has(tag)) {
              tagRelations.set(tag, /* @__PURE__ */ new Set());
            }
            for (const relatedTag of tags) {
              if (relatedTag !== tag) {
                tagRelations.get(tag).add(relatedTag);
              }
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to load collection ${collectionName}:`, error);
      }
    }
    for (const [tagName, count] of tagCounts) {
      const category = tagCategories.get(tagName) || "other";
      const relatedTags = Array.from(tagRelations.get(tagName) || []).sort((a, b) => (tagCounts.get(b) || 0) - (tagCounts.get(a) || 0)).slice(0, 5);
      this.tagCache.set(tagName, {
        name: tagName,
        count,
        category,
        relatedTags,
        color: this.getTagColor(category)
      });
    }
    this.lastUpdate = Date.now();
  }
  /**
   * 从缓存构建统计信息
   */
  buildStatsFromCache() {
    const allTags = Array.from(this.tagCache.values());
    const tagsByCategory = {};
    for (const tag of allTags) {
      if (!tagsByCategory[tag.category]) {
        tagsByCategory[tag.category] = [];
      }
      tagsByCategory[tag.category].push(tag);
    }
    for (const category in tagsByCategory) {
      tagsByCategory[category].sort((a, b) => b.count - a.count);
    }
    const mostPopularTags = allTags.sort((a, b) => b.count - a.count).slice(0, 20);
    const recentTags = mostPopularTags.slice(0, 10).map((tag) => tag.name);
    return {
      totalTags: allTags.length,
      totalUniqueContent: this.calculateUniqueContent(allTags),
      mostPopularTags,
      tagsByCategory,
      recentTags
    };
  }
  /**
   * 根据标签名称和来源集合推断标签分类
   */
  categorizeTag(tagName, collectionName) {
    const tag = tagName.toLowerCase();
    if (tag.includes("ai") || tag.includes("人工智能") || tag.includes("机器学习") || tag.includes("算法") || tag.includes("技术") || tag.includes("数字化")) {
      return "technology";
    }
    if (tag.includes("经济") || tag.includes("市场") || tag.includes("商业") || tag.includes("金融") || tag.includes("投资")) {
      return "economics";
    }
    if (tag.includes("哲学") || tag.includes("伦理") || tag.includes("思考") || tag.includes("价值观") || tag.includes("道德")) {
      return "philosophy";
    }
    if (tag.includes("社会") || tag.includes("文化") || tag.includes("教育") || tag.includes("政策") || tag.includes("治理")) {
      return "society";
    }
    if (tag.includes("研究") || tag.includes("分析") || tag.includes("报告") || tag.includes("框架") || tag.includes("方法")) {
      return "research";
    }
    if (tag.includes("工具") || tag.includes("平台") || tag.includes("系统") || tag.includes("助手") || tag.includes("管理")) {
      return "tools";
    }
    switch (collectionName) {
      case "ai":
        return "technology";
      case "economics":
        return "economics";
      case "philosophy":
        return "philosophy";
      case "products":
        return "tools";
      case "research":
        return "research";
      default:
        return "general";
    }
  }
  /**
   * 获取标签颜色
   */
  getTagColor(category) {
    const colors = {
      technology: "#3b82f6",
      // 蓝色
      economics: "#10b981",
      // 绿色
      philosophy: "#8b5cf6",
      // 紫色
      society: "#f59e0b",
      // 橙色
      research: "#ef4444",
      // 红色
      tools: "#6b7280",
      // 灰色
      general: "#64748b"
      // 石板色
    };
    return colors[category] || colors.general;
  }
  /**
   * 计算唯一内容数量
   */
  calculateUniqueContent(tags) {
    return tags.reduce((sum, tag) => sum + tag.count, 0);
  }
  /**
   * 获取特定标签的详细信息
   */
  async getTagInfo(tagName) {
    if (this.tagCache.size === 0) {
      await this.refreshTagCache();
    }
    return this.tagCache.get(tagName) || null;
  }
  /**
   * 搜索标签
   */
  async searchTags(query) {
    if (this.tagCache.size === 0) {
      await this.refreshTagCache();
    }
    const searchTerm = query.toLowerCase();
    return Array.from(this.tagCache.values()).filter((tag) => tag.name.toLowerCase().includes(searchTerm)).sort((a, b) => b.count - a.count);
  }
  /**
   * 获取标签建议（基于现有标签）
   */
  async getTagSuggestions(partialTag) {
    const searchResults = await this.searchTags(partialTag);
    return searchResults.slice(0, 10).map((tag) => tag.name);
  }
  /**
   * 获取相关标签
   */
  async getRelatedTags(tagName) {
    const tagInfo = await this.getTagInfo(tagName);
    if (!tagInfo) return [];
    const relatedTagInfos = [];
    for (const relatedTagName of tagInfo.relatedTags) {
      const relatedInfo = await this.getTagInfo(relatedTagName);
      if (relatedInfo) {
        relatedTagInfos.push(relatedInfo);
      }
    }
    return relatedTagInfos;
  }
}
const globalTagManager = new TagManager();
export {
  globalTagManager as g
};
