class r{mode;contentId;currentView="edit";isDirty=!1;autoSaveTimer;constructor(t,e){this.mode=t,this.contentId=e,this.bindEvents(),this.initializeEditor()}bindEvents(){document.getElementById("edit-tab")?.addEventListener("click",()=>this.switchView("edit")),document.getElementById("preview-tab")?.addEventListener("click",()=>this.switchView("preview")),document.getElementById("split-tab")?.addEventListener("click",()=>this.switchView("split")),document.querySelectorAll(".toolbar-btn").forEach(n=>{n.addEventListener("click",i=>{const s=i.target.dataset.action;s&&this.executeToolbarAction(s)})}),document.getElementById("save-draft")?.addEventListener("click",()=>this.saveDraft()),document.getElementById("publish-content")?.addEventListener("click",()=>this.publishContent()),document.getElementById("close-dialog")?.addEventListener("click",()=>this.closeDialog()),document.getElementById("confirm-save")?.addEventListener("click",()=>this.confirmSave()),document.getElementById("cancel-save")?.addEventListener("click",()=>this.closeDialog()),document.getElementById("content-editor")?.addEventListener("input",()=>{this.markDirty(),this.updateWordCount(),this.updatePreview(),this.scheduleAutoSave()}),document.getElementById("content-title")?.addEventListener("input",()=>{this.generateSlug(),this.markDirty()}),document.querySelectorAll(".form-input, .form-select, .form-textarea, .checkbox-input").forEach(n=>{n.addEventListener("change",()=>this.markDirty())}),window.addEventListener("beforeunload",n=>{this.isDirty&&(n.preventDefault(),n.returnValue="您有未保存的更改，确定要离开吗？")})}initializeEditor(){this.updateWordCount(),this.updatePreview(),this.switchView("edit")}switchView(t){this.currentView=t,document.querySelectorAll(".tab-button").forEach(n=>n.classList.remove("active")),document.getElementById(`${t}-tab`)?.classList.add("active");const e=document.getElementById("edit-pane"),o=document.getElementById("preview-pane");if(!(!e||!o))switch(e.classList.remove("active","split"),o.classList.remove("active","split"),t){case"edit":e.classList.add("active");break;case"preview":o.classList.add("active"),this.updatePreview();break;case"split":e.classList.add("split"),o.classList.add("split"),this.updatePreview();break}}executeToolbarAction(t){const e=document.getElementById("content-editor");if(!e)return;const o=e.selectionStart,n=e.selectionEnd,i=e.value.substring(o,n);let s="";switch(t){case"bold":s=`**${i||"粗体文本"}**`;break;case"italic":s=`*${i||"斜体文本"}*`;break;case"heading":s=`## ${i||"标题"}`;break;case"link":s=`[${i||"链接文本"}](URL)`;break;case"image":s=`![${i||"图片描述"}](图片URL)`;break;case"code":s=i.includes(`
`)?`\`\`\`
${i||"代码"}
\`\`\``:`\`${i||"代码"}\``;break;case"list":s=`- ${i||"列表项"}`;break;case"quote":s=`> ${i||"引用内容"}`;break}e.value=e.value.substring(0,o)+s+e.value.substring(n),e.focus();const c=o+s.length;e.setSelectionRange(c,c),this.markDirty(),this.updateWordCount(),this.updatePreview()}generateSlug(){const t=document.getElementById("content-title"),e=document.getElementById("content-slug");if(!t||!e||e.value)return;const o=t.value.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();e.value=o}updateWordCount(){const t=document.getElementById("content-editor"),e=document.getElementById("word-count"),o=document.getElementById("line-count");if(!t||!e||!o)return;const n=t.value,i=n.trim()?n.trim().split(/\s+/).length:0,s=n.split(`
`).length;e.textContent=`${i} 字`,o.textContent=`${s} 行`}async updatePreview(){if(this.currentView==="edit")return;const t=document.getElementById("content-editor"),e=document.getElementById("preview-content");if(!t||!e)return;const o=t.value,n=this.parseMarkdown(o);e.innerHTML=n}parseMarkdown(t){return t.replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/\*\*(.*)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*)\*/gim,"<em>$1</em>").replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2">$1</a>').replace(/`([^`]+)`/gim,"<code>$1</code>").replace(/\n/gim,"<br>")||"<p>开始编写内容...</p>"}markDirty(){this.isDirty=!0;const t=document.getElementById("save-status");t&&(t.textContent="未保存",t.style.color="#ef4444")}markClean(){this.isDirty=!1;const t=document.getElementById("save-status");t&&(t.textContent="已保存",t.style.color="#10b981")}scheduleAutoSave(){this.autoSaveTimer&&clearTimeout(this.autoSaveTimer),this.autoSaveTimer=window.setTimeout(()=>{this.saveDraft(!0)},3e4)}async saveDraft(t=!1){const e=this.collectFormData();e.draft=!0;try{await this.saveContent(e),t||alert("草稿保存成功！")}catch(o){t||alert("保存失败："+o)}}publishContent(){const t=this.collectFormData();this.showSaveDialog(t,!1)}showSaveDialog(t,e){const o=document.getElementById("save-dialog"),n=document.getElementById("dialog-title"),i=document.getElementById("dialog-collection"),s=document.getElementById("dialog-status");!o||!n||!i||!s||(n.textContent=t.title||"未命名",i.textContent=this.getCollectionDisplayName(t.collection),s.textContent=e?"草稿":"发布",o.style.display="flex",o._pendingData={...t,draft:e})}closeDialog(){const t=document.getElementById("save-dialog");t&&(t.style.display="none")}async confirmSave(){const e=document.getElementById("save-dialog")?._pendingData;if(e)try{await this.saveContent(e),this.closeDialog(),alert(e.draft?"草稿保存成功！":"内容发布成功！"),e.draft||(window.location.href="/admin/content")}catch(o){alert("保存失败："+o)}}collectFormData(){const t=n=>document.getElementById(n)?.value||"",e=n=>document.getElementById(n)?.checked||!1,o=t("content-tags").split(",").map(n=>n.trim()).filter(n=>n.length>0);return{collection:t("content-collection"),slug:t("content-slug"),title:t("content-title"),description:t("content-description"),tags:o,author:t("content-author"),draft:e("content-draft"),featured:e("content-featured"),content:t("content-editor")}}async saveContent(t){const e=this.mode==="create"?"/api/content/create":`/api/content/${encodeURIComponent(this.contentId)}`,o=this.mode==="create"?"POST":"PUT",n=this.mode==="create"?t:{frontmatter:t,content:t.content},i=await fetch(e,{method:o,headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!i.ok){const c=await i.json();throw new Error(c.error||"保存失败")}const s=await i.json();return this.mode==="create"&&(this.mode="edit",this.contentId=s.id,window.history.replaceState({},"",`/admin/content/edit/${encodeURIComponent(s.id)}`)),this.markClean(),s}getCollectionDisplayName(t){return{news:"动态资讯",logs:"研究日志",research:"研究报告",reflections:"反思记录",economics:"经济研究",philosophy:"哲学研究",internet:"互联网研究",ai:"AI研究",future:"未来研究",products:"产品发布"}[t]||t}}document.addEventListener("DOMContentLoaded",()=>{const a=window.location.pathname.split("/"),t=a.includes("edit"),e=t?decodeURIComponent(a[a.length-1]):void 0;new r(t?"edit":"create",e)});
