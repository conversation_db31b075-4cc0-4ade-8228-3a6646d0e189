import { c as contentManager } from "../../../assets/contentManager.6eVsAFe1.js";
import { renderers } from "../../../renderers.mjs";
const prerender = false;
const GET = async () => {
  try {
    const stats = await contentManager.getContentStats();
    const response = {
      total: stats.total,
      published: stats.published,
      drafts: stats.drafts,
      featured: stats.featured,
      byCollection: stats.byCollection,
      byAuthor: stats.byAuthor,
      recentActivity: stats.recentActivity.map((activity) => ({
        action: activity.action,
        content: activity.content,
        date: activity.date.toISOString()
      }))
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300"
        // 缓存5分钟
      }
    });
  } catch (error) {
    console.error("内容统计 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET, prerender }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
