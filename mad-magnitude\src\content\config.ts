import { defineCollection, z } from 'astro:content';

// 基础内容 Schema
const baseContentSchema = z.object({
  title: z.object({
    zh: z.string(),
    en: z.string().optional(),
  }),
  description: z.object({
    zh: z.string(),
    en: z.string().optional(),
  }),
  publishDate: z.date(),
  updateDate: z.date().optional(),
  draft: z.boolean().default(false),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  author: z.string().default('Pennfly'),
  readingTime: z.number().optional(),
  relatedContent: z.array(z.string()).optional(), // 关联内容ID
  summary: z.string().optional(), // 内容摘要
});

// 1. 动态资讯 (兼容现有内容格式)
const newsCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    type: z.enum(['research', 'announcement', 'reflection', 'milestone']).default('research'),
    relatedInstitute: z
      .array(z.enum(['economics', 'philosophy', 'internet', 'ai', 'future']))
      .optional(),
    // 兼容现有字段
    category: z.enum(['ai', 'education', 'philosophy', 'technology']).optional(),
    mood: z.enum(['thoughtful', 'critical', 'optimistic', 'analytical']).optional(),
    seo: z
      .object({
        keywords: z.array(z.string()).optional(),
        canonical: z.string().optional(),
      })
      .optional(),
  }),
});

// 2. 研究日志
const logsCollection = defineCollection({
  type: 'content',
  schema: z.object({
    date: z.date(),
    title: z.string(),
    tags: z.array(z.string()).default([]),
    mood: z.enum(['thoughtful', 'critical', 'optimistic', 'analytical']).optional(),
    relatedInstitute: z
      .array(z.enum(['economics', 'philosophy', 'internet', 'ai', 'future']))
      .optional(),
    draft: z.boolean().default(false),
  }),
});

// 3. 经济研究所
const economicsCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    analysisType: z.enum(['market', 'policy', 'theory', 'data']).optional(),
    dataSource: z.string().optional(),
  }),
});

// 4. 哲学研究所
const philosophyCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    philosophyBranch: z
      .enum(['ethics', 'metaphysics', 'epistemology', 'logic', 'aesthetics'])
      .optional(),
    thinkers: z.array(z.string()).default([]),
  }),
});

// 5. 互联网研究所
const internetCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    industry: z.enum(['social', 'ecommerce', 'fintech', 'education', 'entertainment']).optional(),
    companies: z.array(z.string()).default([]),
  }),
});

// 6. 人工智能研究所
const aiCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    aiField: z.enum(['ml', 'nlp', 'cv', 'robotics', 'ethics', 'agi']).optional(),
    techStack: z.array(z.string()).default([]),
    models: z.array(z.string()).default([]),
  }),
});

// 7. 未来研究所
const futureCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    timeHorizon: z.enum(['short', 'medium', 'long']).optional(), // 短期/中期/长期
    domains: z.array(z.string()).default([]), // 涉及领域
    confidence: z.enum(['low', 'medium', 'high']).optional(), // 预测信心度
  }),
});

// 8. 产品发布中心
const productsCollection = defineCollection({
  type: 'content',
  schema: baseContentSchema.extend({
    demo: z.string().url().optional(),
  }),
});

// 9. 数字资源中心 (后期开发)
const resourcesCollection = defineCollection({
  type: 'data',
  schema: z.object({
    title: z.object({
      zh: z.string(),
      en: z.string().optional(),
    }),
    description: z.object({
      zh: z.string(),
      en: z.string().optional(),
    }),
    category: z.enum(['books', 'videos', 'software', 'datasets', 'papers']),
    items: z.array(
      z.object({
        name: z.string(),
        url: z.string().url().optional(),
        description: z.string(),
        tags: z.array(z.string()).default([]),
        rating: z.number().min(1).max(5).optional(),
        notes: z.string().optional(),
      })
    ),
    updateDate: z.date(),
  }),
});

// 10. 模板集合 (用于页面模板和组件示例)
const templatesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    category: z.enum(['layout', 'component', 'page', 'example']),
    tags: z.array(z.string()).default([]),
    updateDate: z.date(),
    draft: z.boolean().default(false),
  }),
});

export const collections = {
  // 核心板块 - 现有内容迁移到这里
  news: newsCollection, // 动态资讯 (包含现有的 research、reflections 内容)
  logs: logsCollection, // 研究日志

  // 保持现有集合的兼容性 (逐步迁移到 news)
  research: newsCollection, // 兼容现有研究内容，类型设为 'research'
  reflections: newsCollection, // 兼容现有反思内容，类型设为 'reflection'
  collections: resourcesCollection, // 兼容现有资源集合

  // 研究所 (新架构，暂时保留定义)
  economics: economicsCollection, // 经济研究所
  philosophy: philosophyCollection, // 哲学研究所
  internet: internetCollection, // 互联网研究所
  ai: aiCollection, // 人工智能研究所
  future: futureCollection, // 未来研究所

  // 功能中心
  products: productsCollection, // 产品发布中心
  resources: resourcesCollection, // 数字资源中心 (后期)
  templates: templatesCollection, // 模板集合
};
