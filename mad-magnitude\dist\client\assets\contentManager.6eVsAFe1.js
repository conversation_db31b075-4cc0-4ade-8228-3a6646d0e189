import { g as getCollection } from "./_astro_content.Cyc5QpB4.js";
class ContentManager {
  static instance;
  contentCache = /* @__PURE__ */ new Map();
  lastCacheUpdate = 0;
  cacheTimeout = 5 * 60 * 1e3;
  // 5分钟缓存
  static getInstance() {
    if (!ContentManager.instance) {
      ContentManager.instance = new ContentManager();
    }
    return ContentManager.instance;
  }
  /**
   * 获取所有内容列表
   */
  async getAllContent(filter) {
    try {
      await this.refreshCacheIfNeeded();
      let items = Array.from(this.contentCache.values());
      if (filter) {
        items = this.applyFilter(items, filter);
      }
      return items.sort((a, b) => {
        const dateA = a.updateDate || a.publishDate;
        const dateB = b.updateDate || b.publishDate;
        return dateB.getTime() - dateA.getTime();
      });
    } catch (error) {
      console.error("Error getting all content:", error);
      return [];
    }
  }
  /**
   * 根据ID获取单个内容
   */
  async getContentById(id) {
    await this.refreshCacheIfNeeded();
    return this.contentCache.get(id) || null;
  }
  /**
   * 创建新内容（简化版本，实际文件操作需要通过API）
   */
  async createContent(collection, slug, frontmatter, content) {
    const now = /* @__PURE__ */ new Date();
    const fullFrontmatter = {
      title: frontmatter.title || "Untitled",
      description: frontmatter.description || "",
      publishDate: frontmatter.publishDate || now,
      updateDate: now,
      draft: frontmatter.draft !== void 0 ? frontmatter.draft : true,
      featured: frontmatter.featured || false,
      tags: frontmatter.tags || [],
      author: frontmatter.author || "Pennfly",
      ...frontmatter
    };
    const contentItem = {
      id: `${collection}/${slug}`,
      collection,
      slug,
      title: fullFrontmatter.title,
      description: fullFrontmatter.description,
      publishDate: new Date(fullFrontmatter.publishDate),
      updateDate: new Date(fullFrontmatter.updateDate),
      draft: fullFrontmatter.draft,
      featured: fullFrontmatter.featured,
      tags: fullFrontmatter.tags,
      author: fullFrontmatter.author,
      content,
      filePath: `src/content/${collection}/${slug}.md`
    };
    this.contentCache.set(contentItem.id, contentItem);
    return contentItem;
  }
  /**
   * 更新内容（简化版本，实际文件操作需要通过API）
   */
  async updateContent(id, frontmatter, content) {
    const existingItem = await this.getContentById(id);
    if (!existingItem) {
      return null;
    }
    const updatedFrontmatter = {
      ...frontmatter,
      updateDate: /* @__PURE__ */ new Date()
    };
    const updatedItem = {
      ...existingItem,
      title: updatedFrontmatter.title || existingItem.title,
      description: updatedFrontmatter.description || existingItem.description,
      updateDate: updatedFrontmatter.updateDate,
      draft: updatedFrontmatter.draft !== void 0 ? updatedFrontmatter.draft : existingItem.draft,
      featured: updatedFrontmatter.featured !== void 0 ? updatedFrontmatter.featured : existingItem.featured,
      tags: updatedFrontmatter.tags || existingItem.tags,
      author: updatedFrontmatter.author || existingItem.author,
      content
    };
    this.contentCache.set(id, updatedItem);
    return updatedItem;
  }
  /**
   * 删除内容（简化版本，实际文件操作需要通过API）
   */
  async deleteContent(id) {
    const item = await this.getContentById(id);
    if (!item) {
      return false;
    }
    try {
      this.contentCache.delete(id);
      return true;
    } catch (error) {
      console.error("删除内容失败:", error);
      return false;
    }
  }
  /**
   * 获取内容统计信息
   */
  async getContentStats() {
    await this.refreshCacheIfNeeded();
    const items = Array.from(this.contentCache.values());
    const published = items.filter((item) => !item.draft);
    const drafts = items.filter((item) => item.draft);
    const featured = items.filter((item) => item.featured);
    const byCollection = {};
    items.forEach((item) => {
      byCollection[item.collection] = (byCollection[item.collection] || 0) + 1;
    });
    const byAuthor = {};
    items.forEach((item) => {
      byAuthor[item.author] = (byAuthor[item.author] || 0) + 1;
    });
    const recentActivity = items.sort((a, b) => {
      const dateA = a.updateDate || a.publishDate;
      const dateB = b.updateDate || b.publishDate;
      return dateB.getTime() - dateA.getTime();
    }).slice(0, 10).map((item) => ({
      action: item.updateDate ? "更新" : "创建",
      content: item.title,
      date: item.updateDate || item.publishDate
    }));
    return {
      total: items.length,
      published: published.length,
      drafts: drafts.length,
      featured: featured.length,
      byCollection,
      byAuthor,
      recentActivity
    };
  }
  /**
   * 搜索内容
   */
  async searchContent(query) {
    await this.refreshCacheIfNeeded();
    const searchTerm = query.toLowerCase();
    const items = Array.from(this.contentCache.values());
    return items.filter((item) => {
      return item.title.toLowerCase().includes(searchTerm) || item.description?.toLowerCase().includes(searchTerm) || item.content.toLowerCase().includes(searchTerm) || item.tags.some((tag) => tag.toLowerCase().includes(searchTerm)) || item.author.toLowerCase().includes(searchTerm);
    });
  }
  /**
   * 刷新缓存（如果需要）
   */
  async refreshCacheIfNeeded() {
    const now = Date.now();
    if (now - this.lastCacheUpdate < this.cacheTimeout && this.contentCache.size > 0) {
      return;
    }
    await this.refreshCache();
  }
  /**
   * 刷新缓存
   */
  async refreshCache() {
    this.contentCache.clear();
    const collections = [
      "news",
      "logs",
      "research",
      "reflections",
      "economics",
      "philosophy",
      "internet",
      "ai",
      "future",
      "products"
    ];
    for (const collectionName of collections) {
      try {
        const collection = await getCollection(collectionName);
        for (const entry of collection) {
          const contentItem = {
            id: `${collectionName}/${entry.slug}`,
            collection: collectionName,
            slug: entry.slug,
            title: this.extractTitle(entry.data),
            description: this.extractDescription(entry.data),
            publishDate: new Date(entry.data.publishDate || entry.data.date || Date.now()),
            updateDate: entry.data.updateDate ? new Date(entry.data.updateDate) : void 0,
            draft: entry.data.draft || false,
            featured: entry.data.featured || false,
            tags: entry.data.tags || [],
            author: entry.data.author || "Pennfly",
            content: "",
            // Content body would need to be rendered separately
            filePath: `src/content/${collectionName}/${entry.slug}.md`
          };
          this.contentCache.set(contentItem.id, contentItem);
        }
      } catch (error) {
        console.warn(`Failed to load collection ${collectionName}:`, error);
      }
    }
    this.lastCacheUpdate = Date.now();
  }
  /**
   * 提取标题（处理多语言格式）
   */
  extractTitle(data) {
    if (typeof data.title === "string") {
      return data.title;
    }
    if (typeof data.title === "object" && data.title.zh) {
      return data.title.zh;
    }
    return "Untitled";
  }
  /**
   * 提取描述（处理多语言格式）
   */
  extractDescription(data) {
    if (typeof data.description === "string") {
      return data.description;
    }
    if (typeof data.description === "object" && data.description.zh) {
      return data.description.zh;
    }
    return void 0;
  }
  /**
   * 应用筛选器
   */
  applyFilter(items, filter) {
    return items.filter((item) => {
      if (filter.collection && item.collection !== filter.collection) {
        return false;
      }
      if (filter.draft !== void 0 && item.draft !== filter.draft) {
        return false;
      }
      if (filter.featured !== void 0 && item.featured !== filter.featured) {
        return false;
      }
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some((tag) => item.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }
      if (filter.author && item.author !== filter.author) {
        return false;
      }
      if (filter.dateRange) {
        const itemDate = item.updateDate || item.publishDate;
        if (itemDate < filter.dateRange.start || itemDate > filter.dateRange.end) {
          return false;
        }
      }
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase();
        const matchesSearch = item.title.toLowerCase().includes(searchTerm) || item.description?.toLowerCase().includes(searchTerm) || item.content.toLowerCase().includes(searchTerm) || item.tags.some((tag) => tag.toLowerCase().includes(searchTerm));
        if (!matchesSearch) {
          return false;
        }
      }
      return true;
    });
  }
  /**
   * 生成 Markdown 文件内容
   */
  generateMarkdownFile(frontmatter, content) {
    const yamlFrontmatter = Object.entries(frontmatter).map(([key, value]) => {
      if (value instanceof Date) {
        return `${key}: ${value.toISOString()}`;
      } else if (Array.isArray(value)) {
        return `${key}: [${value.map((v) => `'${v}'`).join(", ")}]`;
      } else if (typeof value === "string") {
        return `${key}: '${value.replace(/'/g, "''")}'`;
      } else {
        return `${key}: ${value}`;
      }
    }).join("\n");
    return `---
${yamlFrontmatter}
---

${content}`;
  }
  /**
   * 解析 Markdown 文件
   */
  parseMarkdownFile(fileContent) {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
    const match = fileContent.match(frontmatterRegex);
    if (!match) {
      return { frontmatter: {}, content: fileContent };
    }
    const [, frontmatterStr, content] = match;
    const frontmatter = {};
    const lines = frontmatterStr.split("\n");
    for (const line of lines) {
      const colonIndex = line.indexOf(":");
      if (colonIndex === -1) continue;
      const key = line.substring(0, colonIndex).trim();
      const value = line.substring(colonIndex + 1).trim();
      if (value.startsWith("[") && value.endsWith("]")) {
        const arrayContent = value.slice(1, -1);
        frontmatter[key] = arrayContent.split(",").map((item) => item.trim().replace(/^'|'$/g, ""));
      } else if (value === "true" || value === "false") {
        frontmatter[key] = value === "true";
      } else if (!isNaN(Number(value))) {
        frontmatter[key] = Number(value);
      } else if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
        frontmatter[key] = new Date(value);
      } else {
        frontmatter[key] = value.replace(/^'|'$/g, "");
      }
    }
    return { frontmatter, content: content.trim() };
  }
}
const contentManager = ContentManager.getInstance();
export {
  contentManager as c
};
