import { c as createAstro, a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead } from "../../assets/vendor-astro.GLJzaJCN.js";
import { i } from "../../assets/vendor-astro.GLJzaJCN.js";
import "kleur/colors";
import { h as getEntry } from "../../assets/utils.CrlRgzpX.js";
import { $ as $$Layout } from "../../assets/Layout.27NbYwMF.js";
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/ai");
  }
  const article = await getEntry("ai", slug);
  if (!article) {
    return Astro2.redirect("/404");
  }
  const { Content } = await article.render();
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${article.data.title.zh} - 人工智能研究所`, "description": article.data.description.zh }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="min-h-screen bg-gray-50"> <div class="container mx-auto px-6 py-12"> <!-- 面包屑导航 --> <nav class="mb-8 text-sm text-gray-600"> <a href="/" class="hover:text-purple-600">研究院首页</a> <span class="mx-2">›</span> <a href="/ai" class="hover:text-purple-600">人工智能研究所</a> <span class="mx-2">›</span> <span class="text-gray-800">${article.data.title.zh}</span> </nav> <!-- 文章头部 --> <header class="mb-8 rounded-lg border border-gray-200 bg-white p-8 shadow-sm"> <h1 class="mb-4 text-3xl font-bold text-gray-800">${article.data.title.zh}</h1> <p class="mb-6 text-lg text-gray-600">${article.data.description.zh}</p> <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500"> <span>${article.data.publishDate.toLocaleDateString("zh-CN")}</span> ${article.data.readingTime && renderTemplate`<span>阅读时间 ${article.data.readingTime} 分钟</span>`} ${article.data.aiField && renderTemplate`<span class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"> ${article.data.aiField === "ml" ? "机器学习" : article.data.aiField === "nlp" ? "自然语言处理" : article.data.aiField === "cv" ? "计算机视觉" : article.data.aiField === "ethics" ? "AI伦理" : article.data.aiField === "agi" ? "通用AI" : "机器人学"} </span>`} ${article.data.featured && renderTemplate`<span class="rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
精选
</span>`} </div>  ${article.data.techStack && article.data.techStack.length > 0 && renderTemplate`<div class="mt-4"> <div class="mb-2 text-sm text-gray-500">技术栈:</div> <div class="flex flex-wrap gap-2"> ${article.data.techStack.map((tech) => renderTemplate`<span class="rounded bg-blue-100 px-2 py-1 text-sm text-blue-700">${tech}</span>`)} </div> </div>`}  ${article.data.models && article.data.models.length > 0 && renderTemplate`<div class="mt-4"> <div class="mb-2 text-sm text-gray-500">相关模型:</div> <div class="flex flex-wrap gap-2"> ${article.data.models.map((model) => renderTemplate`<span class="rounded bg-indigo-100 px-2 py-1 text-sm text-indigo-700"> ${model} </span>`)} </div> </div>`}  ${article.data.tags.length > 0 && renderTemplate`<div class="mt-4"> <div class="mb-2 text-sm text-gray-500">标签:</div> <div class="flex flex-wrap gap-2"> ${article.data.tags.map((tag) => renderTemplate`<span class="rounded bg-gray-100 px-2 py-1 text-sm text-gray-700">${tag}</span>`)} </div> </div>`} </header> <!-- 文章内容 --> <article class="prose prose-lg mx-auto max-w-none rounded-lg border border-gray-200 bg-white p-8 shadow-sm"> ${renderComponent($$result2, "Content", Content, {})} </article> <!-- 返回按钮 --> <div class="mt-8 text-center"> <a href="/ai" class="inline-block rounded-lg bg-purple-600 px-6 py-2 text-white transition-colors hover:bg-purple-700">
← 返回人工智能研究所
</a> </div> </div> </main> ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/ai/[...slug].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/ai/[...slug].astro";
const $$url = "/ai/[...slug]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
