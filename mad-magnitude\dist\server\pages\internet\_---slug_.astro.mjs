import { b as createAstro, c as createComponent, r as renderComponent, a as renderTemplate, m as maybeRenderHead } from "../../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import { g as getCollection } from "../../assets/_astro_content.Cyc5QpB4.js";
import { $ as $$Layout } from "../../assets/Layout.iGDLAGRN.js";
import { renderers } from "../../renderers.mjs";
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
async function getStaticPaths() {
  const internetArticles = await getCollection("internet");
  return internetArticles.map((article) => ({
    params: { slug: article.slug },
    props: { article }
  }));
}
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { article } = Astro2.props;
  if (!article) {
    return Astro2.redirect("/404");
  }
  const { Content } = await article.render();
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${article.data.title.zh} - 互联网研究所`, "description": article.data.description.zh }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="min-h-screen bg-gray-50"> <div class="container mx-auto px-6 py-12"> <!-- 面包屑导航 --> <nav class="mb-8 text-sm text-gray-600"> <a href="/" class="hover:text-blue-600">研究院首页</a> <span class="mx-2">›</span> <a href="/internet" class="hover:text-blue-600">互联网研究所</a> <span class="mx-2">›</span> <span class="text-gray-800">${article.data.title.zh}</span> </nav> <!-- 文章头部 --> <header class="mb-8 rounded-lg border border-gray-200 bg-white p-8 shadow-sm"> <h1 class="mb-4 text-3xl font-bold text-gray-800">${article.data.title.zh}</h1> <p class="mb-6 text-lg text-gray-600">${article.data.description.zh}</p> <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500"> <span>${article.data.publishDate.toLocaleDateString("zh-CN")}</span> ${article.data.readingTime && renderTemplate`<span>阅读时间 ${article.data.readingTime} 分钟</span>`} ${article.data.industry && renderTemplate`<span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"> ${article.data.industry === "social" ? "社交媒体" : article.data.industry === "ecommerce" ? "电子商务" : article.data.industry === "fintech" ? "金融科技" : article.data.industry === "education" ? "在线教育" : "娱乐产业"} </span>`} ${article.data.featured && renderTemplate`<span class="rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
精选
</span>`} </div>  ${article.data.companies && article.data.companies.length > 0 && renderTemplate`<div class="mt-4"> <div class="mb-2 text-sm text-gray-500">相关公司:</div> <div class="flex flex-wrap gap-2"> ${article.data.companies.map((company) => renderTemplate`<span class="rounded bg-cyan-100 px-2 py-1 text-sm text-cyan-700">${company}</span>`)} </div> </div>`}  ${article.data.tags.length > 0 && renderTemplate`<div class="mt-4"> <div class="mb-2 text-sm text-gray-500">标签:</div> <div class="flex flex-wrap gap-2"> ${article.data.tags.map((tag) => renderTemplate`<span class="rounded bg-gray-100 px-2 py-1 text-sm text-gray-700">${tag}</span>`)} </div> </div>`} </header> <!-- 文章内容 --> <article class="prose prose-lg mx-auto max-w-none rounded-lg border border-gray-200 bg-white p-8 shadow-sm"> ${renderComponent($$result2, "Content", Content, {})} </article> <!-- 返回按钮 --> <div class="mt-8 text-center"> <a href="/internet" class="inline-block rounded-lg bg-blue-600 px-6 py-2 text-white transition-colors hover:bg-blue-700">
← 返回互联网研究所
</a> </div> </div> </main> ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/internet/[...slug].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/internet/[...slug].astro";
const $$url = "/internet/[...slug]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, getStaticPaths, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
