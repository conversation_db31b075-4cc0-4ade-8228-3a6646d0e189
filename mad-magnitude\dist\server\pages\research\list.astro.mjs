import { a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../../assets/vendor-astro.kctgsZae.js";
import { i } from "../../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { e as getCollection } from "../../assets/utils.CcA_tyNa.js";
import { $ as $$Layout } from "../../assets/Layout.DZjfu67Z.js";
const $$List = createComponent(async ($$result, $$props, $$slots) => {
  const research = await getCollection("research");
  const featured = research.filter((p) => p.data.featured && !p.data.draft);
  const recent = research.filter((p) => !p.data.draft).sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "研究文章" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8 max-w-6xl"> <!-- 页面头部 --> <header class="mb-12"> <h1 class="text-3xl md:text-4xl font-bold mb-4">研究文章</h1> <p class="text-gray-600 max-w-3xl">
深入探索学术前沿，分享研究成果和思考。这里汇集了关于人工智能、教育哲学、技术发展等领域的深度分析。
</p> </header> <!-- 精选文章 --> ${featured.length > 0 && renderTemplate`<section class="mb-12"> <h2 class="text-2xl font-bold mb-6">精选文章</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${featured.map((post) => renderTemplate`<article class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"> <div class="p-6"> <div class="flex items-center text-sm text-gray-500 mb-2"> <span>${post.data.publishDate.toLocaleDateString("zh-CN")}</span> <span class="mx-2">•</span> <span>${post.data.author}</span> </div> <h3 class="text-xl font-semibold mb-3">${post.data.title.zh}</h3> ${post.data.title.en && renderTemplate`<p class="text-gray-600 italic mb-4 text-sm">${post.data.title.en}</p>`} <p class="text-gray-700 mb-4">${post.data.description.zh}</p> <div class="flex flex-wrap gap-2 mb-4"> ${post.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"> ${tag} </span>`)} </div> <a${addAttribute(`/research/${post.slug}`, "href")} class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
阅读全文
<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> </article>`)} </div> </section>`} <!-- 最新文章 --> <section> <h2 class="text-2xl font-bold mb-6">最新文章</h2> <div class="space-y-6"> ${recent.map((post) => renderTemplate`<article class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300"> <div class="flex flex-col md:flex-row md:items-start gap-4"> <div class="md:w-2/3"> <div class="flex items-center text-sm text-gray-500 mb-2"> <span>${post.data.publishDate.toLocaleDateString("zh-CN")}</span> <span class="mx-2">•</span> <span>${post.data.author}</span> <span class="mx-2">•</span> <span class="bg-gray-100 px-2 py-1 rounded text-xs">${post.data.category}</span> </div> <h3 class="text-xl font-semibold mb-2"> <a${addAttribute(`/research/${post.slug}`, "href")} class="hover:text-blue-600"> ${post.data.title.zh} </a> </h3> ${post.data.title.en && renderTemplate`<p class="text-gray-600 italic mb-3 text-sm">${post.data.title.en}</p>`} <p class="text-gray-700 mb-4">${post.data.description.zh}</p> <div class="flex flex-wrap gap-2"> ${post.data.tags.map((tag) => renderTemplate`<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"> ${tag} </span>`)} </div> </div> <div class="md:w-1/3 flex justify-center"> <a${addAttribute(`/research/${post.slug}`, "href")} class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
阅读全文
<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> </div> </article>`)} </div> </section> <!-- 分页 --> <div class="mt-12 flex justify-center"> <nav class="flex items-center space-x-2"> <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50" disabled>
上一页
</button> <button class="px-3 py-2 rounded-md bg-blue-600 text-white">1</button> <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200">2</button> <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200">3</button> <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200">
下一页
</button> </nav> </div> </main> ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/research/list.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/research/list.astro";
const $$url = "/research/list";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$List, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
