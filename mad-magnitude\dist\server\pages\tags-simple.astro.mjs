import { c as createComponent, r as renderComponent, a as renderTemplate, m as maybeRenderHead } from "../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import { $ as $$TagCloud } from "../assets/TagCloud.BLtj07sC.js";
import { $ as $$TagList } from "../assets/TagList.B9vDa1Dw.js";
import { $ as $$Layout } from "../assets/Layout.iGDLAGRN.js";
/* empty css                                      */
import { renderers } from "../renderers.mjs";
const $$TagsSimple = createComponent(($$result, $$props, $$slots) => {
  const testTags = [
    "人工智能",
    "机器学习",
    "深度学习",
    "自然语言处理",
    "数字经济",
    "区块链",
    "金融科技",
    "市场分析"
  ];
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "简单标签测试 - Pennfly Private Academy", "description": "测试标签系统的基本功能", "data-astro-cid-3cjhwuyx": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="simple-tags-page" data-astro-cid-3cjhwuyx> <div class="container" data-astro-cid-3cjhwuyx> <header class="page-header" data-astro-cid-3cjhwuyx> <h1 class="page-title" data-astro-cid-3cjhwuyx>简单标签测试</h1> <p class="page-description" data-astro-cid-3cjhwuyx>这个页面用于测试标签系统的基本功能</p> </header> <!-- 标签云测试 --> <section class="test-section" data-astro-cid-3cjhwuyx> <h2 class="section-title" data-astro-cid-3cjhwuyx>标签云组件</h2> ${renderComponent($$result2, "TagCloud", $$TagCloud, { "maxTags": 20, "showCount": true, "size": "medium", "data-astro-cid-3cjhwuyx": true })} </section> <!-- 标签列表测试 --> <section class="test-section" data-astro-cid-3cjhwuyx> <h2 class="section-title" data-astro-cid-3cjhwuyx>标签列表组件</h2> <div class="tag-list-demo" data-astro-cid-3cjhwuyx> <h3 data-astro-cid-3cjhwuyx>默认样式</h3> ${renderComponent($$result2, "TagList", $$TagList, { "tags": testTags, "data-astro-cid-3cjhwuyx": true })} <h3 data-astro-cid-3cjhwuyx>紧凑样式</h3> ${renderComponent($$result2, "TagList", $$TagList, { "tags": testTags.slice(0, 6), "variant": "compact", "data-astro-cid-3cjhwuyx": true })} <h3 data-astro-cid-3cjhwuyx>徽章样式</h3> ${renderComponent($$result2, "TagList", $$TagList, { "tags": testTags.slice(0, 5), "variant": "badge", "data-astro-cid-3cjhwuyx": true })} </div> </section> </div> </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/tags-simple.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/tags-simple.astro";
const $$url = "/tags-simple";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$TagsSimple, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
