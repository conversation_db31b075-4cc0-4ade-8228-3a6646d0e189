import { c as createAstro, a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../../assets/vendor-astro.GLJzaJCN.js";
import { i } from "../../assets/vendor-astro.GLJzaJCN.js";
import "kleur/colors";
import { h as getEntry, e as getCollection, j as formatDate } from "../../assets/utils.CrlRgzpX.js";
import { $ as $$Layout } from "../../assets/Layout.27NbYwMF.js";
/* empty css                                    */
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/products");
  }
  const product = await getEntry("products", slug);
  if (!product) {
    return Astro2.redirect("/404");
  }
  const { Content } = await product.render();
  const updateDate = product.data.updateDate || product.data.publishDate;
  const allProducts = await getCollection("products");
  const relatedProducts = allProducts.filter((p) => p.slug !== product.slug).filter((p) => {
    const commonTags = p.data.tags.some((tag) => product.data.tags.includes(tag));
    return commonTags;
  }).slice(0, 3);
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${product.data.title.zh} - 产品发布中心`, "description": product.data.description.zh, "data-astro-cid-uq5bhyez": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="mx-auto max-w-4xl" data-astro-cid-uq5bhyez> <!-- 返回链接 --> <nav class="mb-8" data-astro-cid-uq5bhyez> <a href="/products" class="inline-flex items-center text-blue-600 transition-colors hover:text-blue-800" data-astro-cid-uq5bhyez>
← 返回产品发布中心
</a> </nav> <!-- 产品头部 --> <header class="mb-12" data-astro-cid-uq5bhyez> <div class="rounded-lg border border-gray-200 bg-white p-8" data-astro-cid-uq5bhyez> <div class="flex flex-col gap-6 lg:flex-row" data-astro-cid-uq5bhyez> <!-- 基本信息 --> <div class="flex-1" data-astro-cid-uq5bhyez> <div class="mb-4 flex items-center gap-3" data-astro-cid-uq5bhyez> <h1 class="text-3xl font-bold text-gray-900" data-astro-cid-uq5bhyez> ${product.data.title.zh} </h1> ${product.data.featured && renderTemplate`<span class="rounded-full bg-yellow-100 px-3 py-1 text-sm font-medium text-yellow-800" data-astro-cid-uq5bhyez>
⭐ 精选产品
</span>`} </div> <div class="mb-6 flex items-center gap-4 text-sm text-gray-600" data-astro-cid-uq5bhyez> <span data-astro-cid-uq5bhyez>发布于 ${formatDate(product.data.publishDate)}</span> ${product.data.updateDate && renderTemplate`<span data-astro-cid-uq5bhyez>更新于 ${formatDate(updateDate)}</span>`} <span data-astro-cid-uq5bhyez>作者：${product.data.author}</span> </div> <p class="mb-6 text-lg leading-relaxed text-gray-700" data-astro-cid-uq5bhyez> ${product.data.description.zh} </p> <!-- 标签 --> ${product.data.tags.length > 0 && renderTemplate`<div data-astro-cid-uq5bhyez> <h3 class="mb-3 text-sm font-semibold text-gray-900" data-astro-cid-uq5bhyez>产品标签</h3> <div class="flex flex-wrap gap-2" data-astro-cid-uq5bhyez> ${product.data.tags.map((tag) => renderTemplate`<span class="rounded-lg bg-blue-50 px-3 py-1 text-sm font-medium text-blue-700" data-astro-cid-uq5bhyez> ${tag} </span>`)} </div> </div>`} </div> <!-- 操作面板 --> <div class="lg:w-64" data-astro-cid-uq5bhyez> <div class="rounded-lg bg-gray-50 p-6" data-astro-cid-uq5bhyez> <h3 class="mb-4 text-lg font-semibold text-gray-900" data-astro-cid-uq5bhyez>产品链接</h3> <div class="space-y-3" data-astro-cid-uq5bhyez> ${product.data.demo && renderTemplate`<a${addAttribute(product.data.demo, "href")} target="_blank" rel="noopener noreferrer" class="inline-flex w-full items-center justify-center rounded-lg bg-green-600 px-4 py-3 font-medium text-white transition-colors hover:bg-green-700" data-astro-cid-uq5bhyez>
🚀 立即体验
</a>`} <button onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href).then(() => alert('链接已复制到剪贴板'))" class="inline-flex w-full items-center justify-center rounded-lg border border-gray-300 px-4 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50" data-astro-cid-uq5bhyez>
🔗 分享产品
</button> </div> <!-- 产品信息 --> <div class="mt-6 border-t border-gray-200 pt-6" data-astro-cid-uq5bhyez> <h4 class="mb-3 text-sm font-semibold text-gray-900" data-astro-cid-uq5bhyez>产品信息</h4> <div class="space-y-2 text-sm" data-astro-cid-uq5bhyez> <div class="flex justify-between" data-astro-cid-uq5bhyez> <span class="text-gray-600" data-astro-cid-uq5bhyez>发布时间</span> <span class="text-gray-900" data-astro-cid-uq5bhyez>${formatDate(product.data.publishDate)}</span> </div> ${product.data.updateDate && renderTemplate`<div class="flex justify-between" data-astro-cid-uq5bhyez> <span class="text-gray-600" data-astro-cid-uq5bhyez>最后更新</span> <span class="text-gray-900" data-astro-cid-uq5bhyez>${formatDate(product.data.updateDate)}</span> </div>`} <div class="flex justify-between" data-astro-cid-uq5bhyez> <span class="text-gray-600" data-astro-cid-uq5bhyez>开发者</span> <span class="text-gray-900" data-astro-cid-uq5bhyez>${product.data.author}</span> </div> </div> </div> </div> </div> </div> </div> </header> <!-- 产品内容 --> <main class="mb-12" data-astro-cid-uq5bhyez> <div class="rounded-lg border border-gray-200 bg-white p-8" data-astro-cid-uq5bhyez> <div class="prose prose-lg max-w-none" data-astro-cid-uq5bhyez> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-uq5bhyez": true })} </div> </div> </main> <!-- 相关产品 --> ${relatedProducts.length > 0 && renderTemplate`<section class="mb-12" data-astro-cid-uq5bhyez> <div class="rounded-lg border border-gray-200 bg-white p-8" data-astro-cid-uq5bhyez> <h2 class="mb-6 text-2xl font-bold text-gray-900" data-astro-cid-uq5bhyez>相关产品</h2> <div class="grid gap-6 md:grid-cols-3" data-astro-cid-uq5bhyez> ${relatedProducts.map((relatedProduct) => {
    return renderTemplate`<article class="rounded-lg border border-gray-200 p-6 transition-shadow hover:shadow-md" data-astro-cid-uq5bhyez> ${relatedProduct.data.featured && renderTemplate`<div class="mb-3" data-astro-cid-uq5bhyez> <span class="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800" data-astro-cid-uq5bhyez>
⭐ 精选
</span> </div>`} <h3 class="mb-2 text-lg font-semibold text-gray-900" data-astro-cid-uq5bhyez> <a${addAttribute(`/products/${relatedProduct.slug}`, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-uq5bhyez> ${relatedProduct.data.title.zh} </a> </h3> <p class="mb-4 line-clamp-3 text-sm text-gray-600" data-astro-cid-uq5bhyez> ${relatedProduct.data.description.zh} </p> <div class="flex flex-wrap gap-1" data-astro-cid-uq5bhyez> ${relatedProduct.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600" data-astro-cid-uq5bhyez> ${tag} </span>`)} ${relatedProduct.data.tags.length > 3 && renderTemplate`<span class="text-xs text-gray-500" data-astro-cid-uq5bhyez>
+${relatedProduct.data.tags.length - 3} </span>`} </div> </article>`;
  })} </div> </div> </section>`} <!-- 返回顶部和导航 --> <div class="flex items-center justify-between" data-astro-cid-uq5bhyez> <a href="/products" class="inline-flex items-center rounded-lg bg-gray-600 px-6 py-3 text-white transition-colors hover:bg-gray-700" data-astro-cid-uq5bhyez>
← 返回产品列表
</a> <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="inline-flex items-center rounded-lg border border-gray-300 px-6 py-3 text-gray-700 transition-colors hover:bg-gray-50" data-astro-cid-uq5bhyez>
↑ 返回顶部
</button> </div> </div> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/products/[...slug].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/products/[...slug].astro";
const $$url = "/products/[...slug]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
