import { g as globalTagManager } from "../../../assets/tagManager.CZWPi9GW.js";
import { renderers } from "../../../renderers.mjs";
const GET = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const query = searchParams.get("q");
    const limit = parseInt(searchParams.get("limit") || "10");
    const category = searchParams.get("category");
    if (!query || query.trim().length === 0) {
      return new Response(
        JSON.stringify({
          error: "Query parameter is required",
          code: "MISSING_QUERY"
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }
    const searchResults = await globalTagManager.searchTags(query.trim());
    let filteredResults = searchResults;
    if (category && category !== "all") {
      filteredResults = searchResults.filter((tag) => tag.category === category);
    }
    const limitedResults = filteredResults.slice(0, limit);
    const response = {
      query: query.trim(),
      total: filteredResults.length,
      results: limitedResults.map((tag) => ({
        name: tag.name,
        count: tag.count,
        category: tag.category,
        categoryDisplayName: getCategoryDisplayName(tag.category),
        color: tag.color,
        url: `/tags/${encodeURIComponent(tag.name)}`,
        relatedTags: tag.relatedTags.slice(0, 3)
      })),
      suggestions: await generateSearchSuggestions(query.trim(), searchResults)
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300"
        // 缓存5分钟
      }
    });
  } catch (error) {
    console.error("标签搜索 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }
};
async function generateSearchSuggestions(query, allResults) {
  const suggestions = [];
  const queryLower = query.toLowerCase();
  const exactMatches = allResults.filter((tag) => tag.name.toLowerCase() === queryLower);
  const startsWith = allResults.filter(
    (tag) => tag.name.toLowerCase().startsWith(queryLower) && tag.name.toLowerCase() !== queryLower
  );
  const contains = allResults.filter(
    (tag) => tag.name.toLowerCase().includes(queryLower) && !tag.name.toLowerCase().startsWith(queryLower)
  );
  const relatedTags = /* @__PURE__ */ new Set();
  [...exactMatches, ...startsWith].forEach((tag) => {
    tag.relatedTags.forEach((related) => {
      if (related.toLowerCase().includes(queryLower)) {
        relatedTags.add(related);
      }
    });
  });
  suggestions.push(
    ...exactMatches.slice(0, 2).map((tag) => tag.name),
    ...startsWith.slice(0, 3).map((tag) => tag.name),
    ...contains.slice(0, 3).map((tag) => tag.name),
    ...Array.from(relatedTags).slice(0, 2)
  );
  return [...new Set(suggestions)].slice(0, 8);
}
function getCategoryDisplayName(category) {
  const names = {
    technology: "技术",
    economics: "经济",
    philosophy: "哲学",
    society: "社会",
    research: "研究",
    tools: "工具",
    general: "通用"
  };
  return names[category] || category;
}
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
