/* 学术风格样式 */

/* 学术内容样式 - 已移除数学公式支持 */

/* 代码高亮样式已移除 */

/* 学术内容基础样式 */
.academic-content {
  max-width: none;

  /* 段落样式 */
  p {
    margin-bottom: 1rem;
    line-height: 1.625;
    color: rgb(55 65 81);
    text-align: justify;
    text-justify: inter-ideograph;
  }

  /* 标题样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 700;
    color: rgb(17 24 39);
    line-height: 1.3;
  }

  h1 {
    margin-bottom: 1.5rem;
    font-size: 1.875rem;
  }
  h2 {
    margin-bottom: 1.25rem;
    font-size: 1.5rem;
  }
  h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
  }
  h4 {
    margin-bottom: 0.75rem;
    font-size: 1.125rem;
  }
  h5 {
    margin-bottom: 0.75rem;
    font-size: 1rem;
  }
  h6 {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  /* 列表样式 */
  ul,
  ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    margin-bottom: 0.5rem;
    line-height: 1.625;
  }

  /* 引用样式 */
  blockquote {
    margin-bottom: 1rem;
    border-left: 4px solid rgb(59 130 246);
    background-color: rgb(239 246 255);
    padding: 0.5rem 1rem;
    color: rgb(55 65 81);
    font-style: italic;
  }

  /* 链接样式 */
  a {
    color: rgb(37 99 235);
    text-decoration: underline;
    transition: color 0.15s ease-in-out;
  }

  a:hover {
    color: rgb(30 64 175);
  }

  /* 强调样式 */
  strong,
  b {
    font-weight: 600;
    color: rgb(17 24 39);
  }

  em,
  i {
    font-style: italic;
  }

  /* 代码样式 */
  code {
    border-radius: 0.25rem;
    background-color: rgb(243 244 246);
    padding: 0.25rem 0.5rem;
    font-family:
      ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.875rem;
    color: rgb(31 41 55);
  }

  pre {
    margin-bottom: 1rem;
    overflow-x: auto;
    border-radius: 0.5rem;
    background-color: rgb(17 24 39);
    padding: 1rem;
    color: rgb(243 244 246);
  }

  pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
  }

  /* 表格样式 */
  table {
    margin-bottom: 1rem;
    width: 100%;
    border-collapse: collapse;
  }

  th,
  td {
    border: 1px solid rgb(209 213 219);
    padding: 0.5rem 1rem;
    text-align: left;
  }

  th {
    background-color: rgb(243 244 246);
    font-weight: 600;
  }

  /* 分隔线样式 */
  hr {
    margin: 2rem 0;
    border-top: 1px solid rgb(209 213 219);
  }

  /* 图片样式 */
  img {
    margin-bottom: 1rem;
    height: auto;
    max-width: 100%;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }

  /* 图片说明样式 */
  .image-caption {
    margin-top: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: rgb(75 85 99);
    font-style: italic;
  }
}

/* 数学公式特殊样式 */
.academic-content {
  /* 数学公式支持已移除 */
}

/* 代码块特殊样式 */
.academic-content {
  pre[class*='language-'] {
    position: relative;
  }

  pre[class*='language-']::before {
    content: attr(data-language);
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    color: rgb(156 163 175);
    text-transform: uppercase;
  }

  /* 行号支持 */
  .line-numbers {
    position: relative;
    padding-left: 3rem;
  }

  .line-numbers .line-numbers-rows {
    position: absolute;
    top: 0;
    left: 0;
    width: 2.5rem;
    text-align: right;
    font-size: 0.875rem;
    line-height: 1.5;
    color: rgb(107 114 128);
    pointer-events: none;
    user-select: none;
  }
}

/* 学术引用样式 */
.academic-content {
  /* 脚注 */
  .footnote {
    margin-top: 2rem;
    border-top: 1px solid rgb(229 231 235);
    padding-top: 1rem;
    font-size: 0.875rem;
    color: rgb(75 85 99);
  }

  .footnote-ref {
    color: rgb(37 99 235);
    text-decoration: none;
  }

  .footnote-ref::before {
    content: '[';
  }

  .footnote-ref::after {
    content: ']';
  }

  /* 参考文献 */
  .references {
    margin-top: 2rem;
    border-top: 1px solid rgb(229 231 235);
    padding-top: 1rem;
  }

  .references h3 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .references ol {
    list-style-type: decimal;
    padding-left: 1.5rem;
  }

  .references ol li {
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.625;
  }
}

/* 目录导航样式 */
.table-of-contents {
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid rgb(229 231 235);
  background-color: rgb(249 250 251);
  padding: 1rem;
}

.table-of-contents h3 {
  margin-bottom: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: rgb(17 24 39);
}

.table-of-contents ul {
  list-style: none;
  padding-left: 0;
}

.table-of-contents ul li {
  margin-bottom: 0.25rem;
}

.table-of-contents ul li a {
  display: block;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  color: rgb(55 65 81);
  text-decoration: none;
  transition:
    background-color 0.15s ease-in-out,
    color 0.15s ease-in-out;
}

.table-of-contents ul li a:hover {
  background-color: rgb(239 246 255);
  color: rgb(37 99 235);
}

/* 嵌套层级 */
.table-of-contents ul li ul {
  margin-top: 0.25rem;
  padding-left: 1rem;
}

.table-of-contents ul li ul a {
  font-size: 0.875rem;
  color: rgb(75 85 99);
}

/* 阅读进度指示器 */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 50;
  height: 0.25rem;
  width: 100%;
  background-color: rgb(229 231 235);
}

.reading-progress .progress-bar {
  height: 100%;
  background-color: rgb(37 99 235);
  transition: all 0.15s ease-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .academic-content h1 {
    font-size: 1.5rem;
  }

  .academic-content h2 {
    font-size: 1.25rem;
  }

  .academic-content h3 {
    font-size: 1.125rem;
  }

  /* 移动端数学公式支持已移除 */

  .academic-content pre {
    font-size: 0.875rem;
  }

  .academic-content table {
    font-size: 0.875rem;
  }

  .table-of-contents {
    font-size: 0.875rem;
  }
}

/* 打印样式 */
@media print {
  .academic-content {
    color: black;
  }

  .academic-content a {
    color: black;
    text-decoration: none;
  }

  .academic-content a::after {
    content: ' (' attr(href) ')';
    font-size: 0.875rem;
  }

  .academic-content pre {
    border: 1px solid rgb(156 163 175);
    background-color: white;
    color: black;
  }

  /* 打印时数学公式支持已移除 */

  .reading-progress,
  .table-of-contents {
    display: none;
  }
}
