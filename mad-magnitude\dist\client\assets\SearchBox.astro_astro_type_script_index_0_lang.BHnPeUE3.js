function F(t){return Array.isArray?Array.isArray(t):ce(t)==="[object Array]"}function ge(t){if(typeof t=="string")return t;let e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function Ae(t){return t==null?"":ge(t)}function M(t){return typeof t=="string"}function re(t){return typeof t=="number"}function pe(t){return t===!0||t===!1||Ee(t)&&ce(t)=="[object Boolean]"}function ie(t){return typeof t=="object"}function Ee(t){return ie(t)&&t!==null}function E(t){return t!=null}function j(t){return!t.trim().length}function ce(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const me="Incorrect 'index' type",Ce=t=>`Invalid value for key ${t}`,Be=t=>`Pattern length exceeds max of ${t}.`,Me=t=>`Missing ${t} property in key`,ye=t=>`Property 'weight' in key '${t}' must be a positive integer`,X=Object.prototype.hasOwnProperty;class Fe{constructor(e){this._keys=[],this._keyMap={};let s=0;e.forEach(r=>{let n=ue(r);this._keys.push(n),this._keyMap[n.id]=n,s+=n.weight}),this._keys.forEach(r=>{r.weight/=s})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function ue(t){let e=null,s=null,r=null,n=1,i=null;if(M(t)||F(t))r=t,e=Z(t),s=P(t);else{if(!X.call(t,"name"))throw new Error(Me("name"));const c=t.name;if(r=c,X.call(t,"weight")&&(n=t.weight,n<=0))throw new Error(ye(c));e=Z(c),s=P(c),i=t.getFn}return{path:e,id:s,weight:n,src:r,getFn:i}}function Z(t){return F(t)?t:t.split(".")}function P(t){return F(t)?t.join("."):t}function De(t,e){let s=[],r=!1;const n=(i,c,u)=>{if(E(i))if(!c[u])s.push(i);else{let o=c[u];const a=i[o];if(!E(a))return;if(u===c.length-1&&(M(a)||re(a)||pe(a)))s.push(Ae(a));else if(F(a)){r=!0;for(let h=0,d=a.length;h<d;h+=1)n(a[h],c,u+1)}else c.length&&n(a,c,u+1)}};return n(t,M(e)?e.split("."):e,0),r?s:s[0]}const xe={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},Ie={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1},Se={location:0,threshold:.6,distance:100},be={useExtendedSearch:!1,getFn:De,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var l={...Ie,...xe,...Se,...be};const _e=/[^ ]+/g;function we(t=1,e=3){const s=new Map,r=Math.pow(10,e);return{get(n){const i=n.match(_e).length;if(s.has(i))return s.get(i);const c=1/Math.pow(i,.5*t),u=parseFloat(Math.round(c*r)/r);return s.set(i,u),u},clear(){s.clear()}}}class Q{constructor({getFn:e=l.getFn,fieldNormWeight:s=l.fieldNormWeight}={}){this.norm=we(s,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((s,r)=>{this._keysMap[s.id]=r})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,M(this.docs[0])?this.docs.forEach((e,s)=>{this._addString(e,s)}):this.docs.forEach((e,s)=>{this._addObject(e,s)}),this.norm.clear())}add(e){const s=this.size();M(e)?this._addString(e,s):this._addObject(e,s)}removeAt(e){this.records.splice(e,1);for(let s=e,r=this.size();s<r;s+=1)this.records[s].i-=1}getValueForItemAtKeyId(e,s){return e[this._keysMap[s]]}size(){return this.records.length}_addString(e,s){if(!E(e)||j(e))return;let r={v:e,i:s,n:this.norm.get(e)};this.records.push(r)}_addObject(e,s){let r={i:s,$:{}};this.keys.forEach((n,i)=>{let c=n.getFn?n.getFn(e):this.getFn(e,n.path);if(E(c)){if(F(c)){let u=[];const o=[{nestedArrIndex:-1,value:c}];for(;o.length;){const{nestedArrIndex:a,value:h}=o.pop();if(E(h))if(M(h)&&!j(h)){let d={v:h,i:a,n:this.norm.get(h)};u.push(d)}else F(h)&&h.forEach((d,f)=>{o.push({nestedArrIndex:f,value:d})})}r.$[i]=u}else if(M(c)&&!j(c)){let u={v:c,n:this.norm.get(c)};r.$[i]=u}}}),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function oe(t,e,{getFn:s=l.getFn,fieldNormWeight:r=l.fieldNormWeight}={}){const n=new Q({getFn:s,fieldNormWeight:r});return n.setKeys(t.map(ue)),n.setSources(e),n.create(),n}function Le(t,{getFn:e=l.getFn,fieldNormWeight:s=l.fieldNormWeight}={}){const{keys:r,records:n}=t,i=new Q({getFn:e,fieldNormWeight:s});return i.setKeys(r),i.setIndexRecords(n),i}function v(t,{errors:e=0,currentLocation:s=0,expectedLocation:r=0,distance:n=l.distance,ignoreLocation:i=l.ignoreLocation}={}){const c=e/t.length;if(i)return c;const u=Math.abs(r-s);return n?c+u/n:u?1:c}function ke(t=[],e=l.minMatchCharLength){let s=[],r=-1,n=-1,i=0;for(let c=t.length;i<c;i+=1){let u=t[i];u&&r===-1?r=i:!u&&r!==-1&&(n=i-1,n-r+1>=e&&s.push([r,n]),r=-1)}return t[i-1]&&i-r>=e&&s.push([r,i-1]),s}const _=32;function $e(t,e,s,{location:r=l.location,distance:n=l.distance,threshold:i=l.threshold,findAllMatches:c=l.findAllMatches,minMatchCharLength:u=l.minMatchCharLength,includeMatches:o=l.includeMatches,ignoreLocation:a=l.ignoreLocation}={}){if(e.length>_)throw new Error(Be(_));const h=e.length,d=t.length,f=Math.max(0,Math.min(r,d));let g=i,A=f;const p=u>1||o,C=p?Array(d):[];let D;for(;(D=t.indexOf(e,A))>-1;){let m=v(e,{currentLocation:D,expectedLocation:f,distance:n,ignoreLocation:a});if(g=Math.min(m,g),A=D+h,p){let x=0;for(;x<h;)C[D+x]=1,x+=1}}A=-1;let y=[],w=1,b=h+d;const fe=1<<h-1;for(let m=0;m<h;m+=1){let x=0,I=b;for(;x<I;)v(e,{errors:m,currentLocation:f+I,expectedLocation:f,distance:n,ignoreLocation:a})<=g?x=I:b=I,I=Math.floor((b-x)/2+x);b=I;let U=Math.max(1,f-I+1),T=c?d:Math.min(f+I,d)+h,L=Array(T+2);L[T+1]=(1<<m)-1;for(let B=T;B>=U;B-=1){let $=B-1,J=s[t.charAt($)];if(p&&(C[$]=+!!J),L[B]=(L[B+1]<<1|1)&J,m&&(L[B]|=(y[B+1]|y[B])<<1|1|y[B+1]),L[B]&fe&&(w=v(e,{errors:m,currentLocation:$,expectedLocation:f,distance:n,ignoreLocation:a}),w<=g)){if(g=w,A=$,A<=f)break;U=Math.max(1,2*f-A)}}if(v(e,{errors:m+1,currentLocation:f,expectedLocation:f,distance:n,ignoreLocation:a})>g)break;y=L}const N={isMatch:A>=0,score:Math.max(.001,w)};if(p){const m=ke(C,u);m.length?o&&(N.indices=m):N.isMatch=!1}return N}function ve(t){let e={};for(let s=0,r=t.length;s<r;s+=1){const n=t.charAt(s);e[n]=(e[n]||0)|1<<r-s-1}return e}const R=String.prototype.normalize?t=>t.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):t=>t;class ae{constructor(e,{location:s=l.location,threshold:r=l.threshold,distance:n=l.distance,includeMatches:i=l.includeMatches,findAllMatches:c=l.findAllMatches,minMatchCharLength:u=l.minMatchCharLength,isCaseSensitive:o=l.isCaseSensitive,ignoreDiacritics:a=l.ignoreDiacritics,ignoreLocation:h=l.ignoreLocation}={}){if(this.options={location:s,threshold:r,distance:n,includeMatches:i,findAllMatches:c,minMatchCharLength:u,isCaseSensitive:o,ignoreDiacritics:a,ignoreLocation:h},e=o?e:e.toLowerCase(),e=a?R(e):e,this.pattern=e,this.chunks=[],!this.pattern.length)return;const d=(g,A)=>{this.chunks.push({pattern:g,alphabet:ve(g),startIndex:A})},f=this.pattern.length;if(f>_){let g=0;const A=f%_,p=f-A;for(;g<p;)d(this.pattern.substr(g,_),g),g+=_;if(A){const C=f-_;d(this.pattern.substr(C),C)}}else d(this.pattern,0)}searchIn(e){const{isCaseSensitive:s,ignoreDiacritics:r,includeMatches:n}=this.options;if(e=s?e:e.toLowerCase(),e=r?R(e):e,this.pattern===e){let p={isMatch:!0,score:0};return n&&(p.indices=[[0,e.length-1]]),p}const{location:i,distance:c,threshold:u,findAllMatches:o,minMatchCharLength:a,ignoreLocation:h}=this.options;let d=[],f=0,g=!1;this.chunks.forEach(({pattern:p,alphabet:C,startIndex:D})=>{const{isMatch:y,score:w,indices:b}=$e(e,p,C,{location:i+D,distance:c,threshold:u,findAllMatches:o,minMatchCharLength:a,includeMatches:n,ignoreLocation:h});y&&(g=!0),f+=w,y&&b&&(d=[...d,...b])});let A={isMatch:g,score:g?f/this.chunks.length:1};return g&&n&&(A.indices=d),A}}class S{constructor(e){this.pattern=e}static isMultiMatch(e){return q(e,this.multiRegex)}static isSingleMatch(e){return q(e,this.singleRegex)}search(){}}function q(t,e){const s=t.match(e);return s?s[1]:null}class Re extends S{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){const s=e===this.pattern;return{isMatch:s,score:s?0:1,indices:[0,this.pattern.length-1]}}}class Oe extends S{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){const r=e.indexOf(this.pattern)===-1;return{isMatch:r,score:r?0:1,indices:[0,e.length-1]}}}class Ne extends S{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){const s=e.startsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,this.pattern.length-1]}}}class Te extends S{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){const s=!e.startsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,e.length-1]}}}class je extends S{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){const s=e.endsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[e.length-this.pattern.length,e.length-1]}}}class Pe extends S{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){const s=!e.endsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,e.length-1]}}}class he extends S{constructor(e,{location:s=l.location,threshold:r=l.threshold,distance:n=l.distance,includeMatches:i=l.includeMatches,findAllMatches:c=l.findAllMatches,minMatchCharLength:u=l.minMatchCharLength,isCaseSensitive:o=l.isCaseSensitive,ignoreDiacritics:a=l.ignoreDiacritics,ignoreLocation:h=l.ignoreLocation}={}){super(e),this._bitapSearch=new ae(e,{location:s,threshold:r,distance:n,includeMatches:i,findAllMatches:c,minMatchCharLength:u,isCaseSensitive:o,ignoreDiacritics:a,ignoreLocation:h})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class le extends S{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let s=0,r;const n=[],i=this.pattern.length;for(;(r=e.indexOf(this.pattern,s))>-1;)s=r+i,n.push([r,s-1]);const c=!!n.length;return{isMatch:c,score:c?0:1,indices:n}}}const K=[Re,le,Ne,Te,Pe,je,Oe,he],ee=K.length,Ke=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,We="|";function ze(t,e={}){return t.split(We).map(s=>{let r=s.trim().split(Ke).filter(i=>i&&!!i.trim()),n=[];for(let i=0,c=r.length;i<c;i+=1){const u=r[i];let o=!1,a=-1;for(;!o&&++a<ee;){const h=K[a];let d=h.isMultiMatch(u);d&&(n.push(new h(d,e)),o=!0)}if(!o)for(a=-1;++a<ee;){const h=K[a];let d=h.isSingleMatch(u);if(d){n.push(new h(d,e));break}}}return n})}const He=new Set([he.type,le.type]);class Ge{constructor(e,{isCaseSensitive:s=l.isCaseSensitive,ignoreDiacritics:r=l.ignoreDiacritics,includeMatches:n=l.includeMatches,minMatchCharLength:i=l.minMatchCharLength,ignoreLocation:c=l.ignoreLocation,findAllMatches:u=l.findAllMatches,location:o=l.location,threshold:a=l.threshold,distance:h=l.distance}={}){this.query=null,this.options={isCaseSensitive:s,ignoreDiacritics:r,includeMatches:n,minMatchCharLength:i,findAllMatches:u,ignoreLocation:c,location:o,threshold:a,distance:h},e=s?e:e.toLowerCase(),e=r?R(e):e,this.pattern=e,this.query=ze(this.pattern,this.options)}static condition(e,s){return s.useExtendedSearch}searchIn(e){const s=this.query;if(!s)return{isMatch:!1,score:1};const{includeMatches:r,isCaseSensitive:n,ignoreDiacritics:i}=this.options;e=n?e:e.toLowerCase(),e=i?R(e):e;let c=0,u=[],o=0;for(let a=0,h=s.length;a<h;a+=1){const d=s[a];u.length=0,c=0;for(let f=0,g=d.length;f<g;f+=1){const A=d[f],{isMatch:p,indices:C,score:D}=A.search(e);if(p){if(c+=1,o+=D,r){const y=A.constructor.type;He.has(y)?u=[...u,...C]:u.push(C)}}else{o=0,c=0,u.length=0;break}}if(c){let f={isMatch:!0,score:o/c};return r&&(f.indices=u),f}}return{isMatch:!1,score:1}}}const W=[];function Ve(...t){W.push(...t)}function z(t,e){for(let s=0,r=W.length;s<r;s+=1){let n=W[s];if(n.condition(t,e))return new n(t,e)}return new ae(t,e)}const O={AND:"$and",OR:"$or"},H={PATH:"$path",PATTERN:"$val"},G=t=>!!(t[O.AND]||t[O.OR]),Ye=t=>!!t[H.PATH],Qe=t=>!F(t)&&ie(t)&&!G(t),te=t=>({[O.AND]:Object.keys(t).map(e=>({[e]:t[e]}))});function de(t,e,{auto:s=!0}={}){const r=n=>{let i=Object.keys(n);const c=Ye(n);if(!c&&i.length>1&&!G(n))return r(te(n));if(Qe(n)){const o=c?n[H.PATH]:i[0],a=c?n[H.PATTERN]:n[o];if(!M(a))throw new Error(Ce(o));const h={keyId:P(o),pattern:a};return s&&(h.searcher=z(a,e)),h}let u={children:[],operator:i[0]};return i.forEach(o=>{const a=n[o];F(a)&&a.forEach(h=>{u.children.push(r(h))})}),u};return G(t)||(t=te(t)),r(t)}function Ue(t,{ignoreFieldNorm:e=l.ignoreFieldNorm}){t.forEach(s=>{let r=1;s.matches.forEach(({key:n,norm:i,score:c})=>{const u=n?n.weight:null;r*=Math.pow(c===0&&u?Number.EPSILON:c,(u||1)*(e?1:i))}),s.score=r})}function Je(t,e){const s=t.matches;e.matches=[],E(s)&&s.forEach(r=>{if(!E(r.indices)||!r.indices.length)return;const{indices:n,value:i}=r;let c={indices:n,value:i};r.key&&(c.key=r.key.src),r.idx>-1&&(c.refIndex=r.idx),e.matches.push(c)})}function Xe(t,e){e.score=t.score}function Ze(t,e,{includeMatches:s=l.includeMatches,includeScore:r=l.includeScore}={}){const n=[];return s&&n.push(Je),r&&n.push(Xe),t.map(i=>{const{idx:c}=i,u={item:e[c],refIndex:c};return n.length&&n.forEach(o=>{o(i,u)}),u})}class k{constructor(e,s={},r){this.options={...l,...s},this.options.useExtendedSearch,this._keyStore=new Fe(this.options.keys),this.setCollection(e,r)}setCollection(e,s){if(this._docs=e,s&&!(s instanceof Q))throw new Error(me);this._myIndex=s||oe(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){E(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const s=[];for(let r=0,n=this._docs.length;r<n;r+=1){const i=this._docs[r];e(i,r)&&(this.removeAt(r),r-=1,n-=1,s.push(i))}return s}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:s=-1}={}){const{includeMatches:r,includeScore:n,shouldSort:i,sortFn:c,ignoreFieldNorm:u}=this.options;let o=M(e)?M(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return Ue(o,{ignoreFieldNorm:u}),i&&o.sort(c),re(s)&&s>-1&&(o=o.slice(0,s)),Ze(o,this._docs,{includeMatches:r,includeScore:n})}_searchStringList(e){const s=z(e,this.options),{records:r}=this._myIndex,n=[];return r.forEach(({v:i,i:c,n:u})=>{if(!E(i))return;const{isMatch:o,score:a,indices:h}=s.searchIn(i);o&&n.push({item:i,idx:c,matches:[{score:a,value:i,norm:u,indices:h}]})}),n}_searchLogical(e){const s=de(e,this.options),r=(u,o,a)=>{if(!u.children){const{keyId:d,searcher:f}=u,g=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(o,d),searcher:f});return g&&g.length?[{idx:a,item:o,matches:g}]:[]}const h=[];for(let d=0,f=u.children.length;d<f;d+=1){const g=u.children[d],A=r(g,o,a);if(A.length)h.push(...A);else if(u.operator===O.AND)return[]}return h},n=this._myIndex.records,i={},c=[];return n.forEach(({$:u,i:o})=>{if(E(u)){let a=r(s,u,o);a.length&&(i[o]||(i[o]={idx:o,item:u,matches:[]},c.push(i[o])),a.forEach(({matches:h})=>{i[o].matches.push(...h)}))}}),c}_searchObjectList(e){const s=z(e,this.options),{keys:r,records:n}=this._myIndex,i=[];return n.forEach(({$:c,i:u})=>{if(!E(c))return;let o=[];r.forEach((a,h)=>{o.push(...this._findMatches({key:a,value:c[h],searcher:s}))}),o.length&&i.push({idx:u,item:c,matches:o})}),i}_findMatches({key:e,value:s,searcher:r}){if(!E(s))return[];let n=[];if(F(s))s.forEach(({v:i,i:c,n:u})=>{if(!E(i))return;const{isMatch:o,score:a,indices:h}=r.searchIn(i);o&&n.push({score:a,key:e,value:i,idx:c,norm:u,indices:h})});else{const{v:i,n:c}=s,{isMatch:u,score:o,indices:a}=r.searchIn(i);u&&n.push({score:o,key:e,value:i,norm:c,indices:a})}return n}}k.version="7.1.0";k.createIndex=oe;k.parseIndex=Le;k.config=l;k.parseQuery=de;Ve(Ge);const qe={keys:[{name:"title",weight:.4},{name:"description",weight:.3},{name:"content",weight:.2},{name:"tags",weight:.1}],threshold:.3,includeScore:!0,includeMatches:!0};let V,se=[];async function et(){try{se=await(await fetch("/search-index.json")).json(),V=new k(se,qe)}catch(t){console.error("Failed to load search index:",t)}}function tt(t){if(!V||t.length<2){Y();return}const e=V.search(t).slice(0,5);st(e)}function st(t){const e=document.getElementById("search-results"),s=e?.querySelector("div"),r=document.getElementById("search-input"),n=document.getElementById("search-status");!e||!s||(t.length===0?(s.innerHTML='<div class="p-4 text-gray-500" role="option">未找到相关内容</div>',n&&(n.textContent="未找到搜索结果")):(s.innerHTML=t.map((i,c)=>{const u=i.item;return`
          <a href="${u.url}" 
             class="block p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:bg-blue-50 focus:outline-none" 
             role="option"
             aria-selected="false"
             data-index="${c}">
            <h3 class="font-semibold text-gray-900 mb-1">${ne(u.title,i.matches)}</h3>
            <p class="text-sm text-gray-600 mb-2">${ne(u.description,i.matches)}</p>
            <div class="flex items-center text-xs text-gray-500">
              <span class="bg-gray-100 px-2 py-1 rounded">${u.category}</span>
              ${u.tags.slice(0,2).map(o=>`<span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded">${o}</span>`).join("")}
            </div>
          </a>
        `}).join(""),n&&(n.textContent=`找到 ${t.length} 个搜索结果`)),e.classList.remove("hidden"),r?.setAttribute("aria-expanded","true"))}function ne(t,e){if(!e)return t;let s=t;return e.forEach(r=>{(r.key==="title"||r.key==="description")&&r.indices.forEach(([n,i])=>{const c=t.substring(n,i+1);s=s.replace(c,`<mark class="bg-yellow-200">${c}</mark>`)})}),s}function Y(){const t=document.getElementById("search-results"),e=document.getElementById("search-input");t?.classList.add("hidden"),e?.setAttribute("aria-expanded","false")}document.addEventListener("DOMContentLoaded",()=>{et();const t=document.getElementById("search-input");let e,s=-1;t?.addEventListener("input",n=>{const c=n.target.value;clearTimeout(e),e=setTimeout(()=>{tt(c),s=-1},300)}),t?.addEventListener("keydown",n=>{const c=document.getElementById("search-results")?.querySelectorAll('[role="option"]');if(!(!c||c.length===0))switch(n.key){case"ArrowDown":n.preventDefault(),s=Math.min(s+1,c.length-1),r(c,s);break;case"ArrowUp":n.preventDefault(),s=Math.max(s-1,-1),r(c,s);break;case"Enter":if(n.preventDefault(),s>=0&&c[s]){const u=c[s];window.location.href=u.href}break;case"Escape":Y(),s=-1;break}});function r(n,i){n.forEach((c,u)=>{const o=u===i;c.setAttribute("aria-selected",o.toString()),o&&c.scrollIntoView({block:"nearest"})})}document.addEventListener("click",n=>{const i=n.target;document.querySelector(".search-container")?.contains(i)||(Y(),s=-1)})});
