<!DOCTYPE html><html lang="zh-CN" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description" content="编辑内容: 平台经济的双刃剑：机遇与挑战并存"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.12.9"><title>编辑: 平台经济的双刃剑：机遇与挑战并存 - Pennfly Private Academy</title><!-- SEO 基础标签 --><meta name="author" content="Pennfly"><meta name="robots" content="index, follow, max-image-preview:large"><!-- Canonical URL --><!-- Open Graph 标签 --><meta property="og:title" content="编辑: 平台经济的双刃剑：机遇与挑战并存 - Pennfly Private Academy"><meta property="og:description" content="编辑内容: 平台经济的双刃剑：机遇与挑战并存"><meta property="og:type" content="website"><meta property="og:url" content="https://pennfly.com/admin/content/edit/internet/platform-economy-analysis/"><meta property="og:image" content="https://pennfly.com/images/og-default.jpg"><meta property="og:site_name" content="Pennfly Private Academy"><meta property="og:locale" content="zh_CN"><!-- Twitter Card 标签 --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="编辑: 平台经济的双刃剑：机遇与挑战并存 - Pennfly Private Academy"><meta name="twitter:description" content="编辑内容: 平台经济的双刃剑：机遇与挑战并存"><meta name="twitter:image" content="https://pennfly.com/images/og-default.jpg"><!-- 发布和更新日期 --><meta property="article:author" content="Pennfly"><!-- 结构化数据 --><!-- 移动端主题色 --><meta name="theme-color" content="#3b82f6"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="default"><!-- 搜索引擎优化 --><meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"><meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"><!-- 额外的 head 内容 --><!-- 性能优化：DNS 预解析 --><link rel="dns-prefetch" href="//fonts.googleapis.com"><link rel="dns-prefetch" href="//cdn.jsdelivr.net"><!-- 性能优化：预加载关键资源 --><link rel="preload" href="/favicon.svg" as="image" type="image/svg+xml"><!-- RSS 订阅 --><link rel="alternate" type="application/rss+xml" title="Pennfly Private Academy RSS Feed" href="/rss.xml"><!-- 内容安全策略 --><meta http-equiv="Content-Security-Policy" content="
      default-src 'self'; 
      style-src 'self' 'unsafe-inline'; 
      font-src 'self' data:; 
      script-src 'self' 'unsafe-inline' 'unsafe-eval'; 
      img-src 'self' data: https:; 
      connect-src 'self'; 
      object-src 'none'; 
      base-uri 'self'; 
      form-action 'self';
    "><!-- 字体样式 --><link rel="stylesheet" href="/assets/styles/admin.B6hnudDf.css">
<link rel="stylesheet" href="/assets/styles/_slug_.Ci54aebO.css">
<style>.edit-content-page[data-astro-cid-n3dwskkx]{height:100vh;background:#f8fafc;padding:1rem}@media (max-width: 768px){.edit-content-page[data-astro-cid-n3dwskkx]{padding:.5rem}}
</style></head> <body class="min-h-screen bg-gray-50 text-gray-900" data-astro-cid-sckkx6r4> <!-- 跳转链接（屏幕阅读器和键盘用户） --> <div class="skip-links" data-astro-cid-sckkx6r4> <a href="#main-content" class="skip-link" data-astro-cid-sckkx6r4> 跳转到主内容 </a> <a href="#navigation" class="skip-link" data-astro-cid-sckkx6r4> 跳转到导航 </a> <a href="#footer" class="skip-link" data-astro-cid-sckkx6r4> 跳转到页脚 </a> </div> <!-- 导航栏 --> <div id="navigation" role="banner" data-astro-cid-sckkx6r4> <header class="sticky top-0 z-50 border-b border-slate-700 shadow-lg" style="background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);"> <div class="container mx-auto px-6"> <div class="flex items-center py-3"> <div class="flex flex-1 items-center"> <!-- Logo --> <a href="/" class="flex flex-shrink-0 items-center space-x-2 transition-all duration-200 hover:opacity-90"> <img src="/ppa-logo.PNG?v=1" alt="Pennfly Private Academy" class="h-5 w-auto" width="20" height="20" loading="eager"> <div class="hidden lg:block"> <div class="text-base leading-tight font-bold text-white">Pennfly Private Academy</div> <div class="-mt-1 text-xs text-blue-100">私人研究院</div> </div> <!-- 移动端和中等屏幕简化标题 --> <div class="block lg:hidden"> <div class="text-sm font-bold text-white">PPA</div> </div> </a> <!-- 桌面端导航 --> <nav class="hidden items-center space-x-1 lg:flex"> <div class="group relative"> <a href="/" class="flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20"> <span class="text-base">🏠</span> <span class="text-sm">首页</span> </a> </div><div class="group relative"> <a href="/news" class="flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20"> <span class="text-base">📰</span> <span class="text-sm">动态资讯</span> </a> </div><div class="group relative"> <a href="/logs" class="flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20"> <span class="text-base">📔</span> <span class="text-sm">研究日志</span> </a> </div><div class="group relative"> <div> <button class="flex items-center space-x-2 rounded-lg border border-white/20 bg-white/10 px-4 py-2 font-medium text-white transition-all duration-200 hover:bg-white/20"> <span class="text-base">🏛️</span> <span class="text-sm">研究所</span> <svg class="h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path> </svg> </button> <div class="invisible absolute top-full left-0 mt-2 w-56 rounded-xl border border-gray-200 bg-white/95 opacity-0 shadow-xl backdrop-blur-sm transition-all duration-300 group-hover:visible group-hover:opacity-100"> <div class="p-3"> <a href="/economics" class="flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 text-slate-700 hover:bg-blue-50/50 hover:text-blue-600"> <span class="text-lg">💰</span> <span class="font-medium">经济研究所</span> </a><a href="/philosophy" class="flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 text-slate-700 hover:bg-blue-50/50 hover:text-blue-600"> <span class="text-lg">🤔</span> <span class="font-medium">哲学研究所</span> </a><a href="/internet" class="flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 text-slate-700 hover:bg-blue-50/50 hover:text-blue-600"> <span class="text-lg">🌐</span> <span class="font-medium">互联网研究所</span> </a><a href="/ai" class="flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 text-slate-700 hover:bg-blue-50/50 hover:text-blue-600"> <span class="text-lg">🤖</span> <span class="font-medium">AI研究所</span> </a><a href="/future" class="flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 text-slate-700 hover:bg-blue-50/50 hover:text-blue-600"> <span class="text-lg">🔮</span> <span class="font-medium">未来研究所</span> </a> </div> </div> </div> </div><div class="group relative"> <a href="/products" class="flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20"> <span class="text-base">🚀</span> <span class="text-sm">产品发布</span> </a> </div><div class="group relative"> <a href="/about" class="flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20"> <span class="text-base">👤</span> <span class="text-sm">关于</span> </a> </div> </nav> <!-- 右侧工具栏 --> <div class="flex items-center space-x-3"> <div class="hidden md:block"> <div class="search-container" role="search"> <label for="search-input" class="sr-only">搜索文章</label> <div class="relative"> <input type="search" id="search-input" placeholder="搜索文章..." class="w-full rounded-lg border border-gray-300 px-4 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500" aria-label="搜索文章" aria-describedby="search-help" aria-expanded="false" aria-autocomplete="list" aria-controls="search-results" autocomplete="off"> <svg class="absolute top-2.5 left-3 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <div id="search-help" class="sr-only">
输入至少2个字符开始搜索。使用上下箭头键导航结果，按回车键选择。
</div> <div id="search-results" class="mt-4 hidden" role="listbox" aria-label="搜索结果"> <div class="max-h-96 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"> <!-- 搜索结果将在这里显示 --> </div> </div> <!-- 搜索状态提示 --> <div id="search-status" class="sr-only" aria-live="polite" aria-atomic="true"></div> </div> <script type="module" src="/assets/SearchBox.astro_astro_type_script_index_0_lang.BEZfOcDA.js"></script> </div> <!-- 移动端菜单按钮 --> <button class="rounded-lg border border-white/20 bg-white/10 p-2 text-white transition-all duration-200 hover:bg-white/20 lg:hidden" id="mobile-menu-button"> <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </div> <!-- 移动端搜索 --> <div id="mobile-search" class="hidden pb-4 md:hidden"> <div class="search-container" role="search"> <label for="search-input" class="sr-only">搜索文章</label> <div class="relative"> <input type="search" id="search-input" placeholder="搜索文章..." class="w-full rounded-lg border border-gray-300 px-4 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500" aria-label="搜索文章" aria-describedby="search-help" aria-expanded="false" aria-autocomplete="list" aria-controls="search-results" autocomplete="off"> <svg class="absolute top-2.5 left-3 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <div id="search-help" class="sr-only">
输入至少2个字符开始搜索。使用上下箭头键导航结果，按回车键选择。
</div> <div id="search-results" class="mt-4 hidden" role="listbox" aria-label="搜索结果"> <div class="max-h-96 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"> <!-- 搜索结果将在这里显示 --> </div> </div> <!-- 搜索状态提示 --> <div id="search-status" class="sr-only" aria-live="polite" aria-atomic="true"></div> </div>  </div> </div> <!-- 移动端菜单 --> <div id="mobile-menu" class="hidden border-t border-gray-200 bg-white lg:hidden"> <div class="container mx-auto px-4 py-4"> <nav class="space-y-2"> <div> <a href="/" class="flex items-center space-x-2 rounded-lg p-3 transition-colors text-slate-700 hover:bg-slate-50"> <span>🏠</span> <span>首页</span> </a> </div><div> <a href="/news" class="flex items-center space-x-2 rounded-lg p-3 transition-colors text-slate-700 hover:bg-slate-50"> <span>📰</span> <span>动态资讯</span> </a> </div><div> <a href="/logs" class="flex items-center space-x-2 rounded-lg p-3 transition-colors text-slate-700 hover:bg-slate-50"> <span>📔</span> <span>研究日志</span> </a> </div><div> <div> <button class="mobile-dropdown-btn flex w-full items-center justify-between rounded-lg p-3 text-slate-700 transition-colors hover:bg-slate-50"> <div class="flex items-center space-x-2"> <span>🏛️</span> <span>研究所</span> </div> <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path> </svg> </button> <div class="mobile-dropdown-content mt-2 ml-4 hidden space-y-1"> <a href="/economics" class="flex items-center space-x-2 rounded-lg p-2 transition-colors text-slate-600 hover:bg-slate-50"> <span>💰</span> <span>经济研究所</span> </a><a href="/philosophy" class="flex items-center space-x-2 rounded-lg p-2 transition-colors text-slate-600 hover:bg-slate-50"> <span>🤔</span> <span>哲学研究所</span> </a><a href="/internet" class="flex items-center space-x-2 rounded-lg p-2 transition-colors text-slate-600 hover:bg-slate-50"> <span>🌐</span> <span>互联网研究所</span> </a><a href="/ai" class="flex items-center space-x-2 rounded-lg p-2 transition-colors text-slate-600 hover:bg-slate-50"> <span>🤖</span> <span>AI研究所</span> </a><a href="/future" class="flex items-center space-x-2 rounded-lg p-2 transition-colors text-slate-600 hover:bg-slate-50"> <span>🔮</span> <span>未来研究所</span> </a> </div> </div> </div><div> <a href="/products" class="flex items-center space-x-2 rounded-lg p-3 transition-colors text-slate-700 hover:bg-slate-50"> <span>🚀</span> <span>产品发布</span> </a> </div><div> <a href="/about" class="flex items-center space-x-2 rounded-lg p-3 transition-colors text-slate-700 hover:bg-slate-50"> <span>👤</span> <span>关于</span> </a> </div> </nav> </div> </div> </div> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const s=document.getElementById("mobile-menu-button"),d=document.getElementById("mobile-menu"),t=document.getElementById("mobile-search");s?.addEventListener("click",()=>{d?.classList.contains("hidden")?(d?.classList.remove("hidden"),t?.classList.remove("hidden")):(d?.classList.add("hidden"),t?.classList.add("hidden"))}),document.querySelectorAll(".mobile-dropdown-btn").forEach(e=>{e.addEventListener("click",()=>{const n=e.nextElementSibling;n?.classList.contains("hidden")?n?.classList.remove("hidden"):n?.classList.add("hidden")})}),document.addEventListener("click",e=>{e.target?.closest("header")||(d?.classList.add("hidden"),t?.classList.add("hidden"))})});</script> </header> </div> <!-- 主要内容 --> <main id="main-content" class="container mx-auto max-w-6xl px-4 py-8" role="main" tabindex="-1" data-astro-cid-sckkx6r4>  <main class="edit-content-page" data-astro-cid-n3dwskkx> <div class="content-editor" data-astro-cid-mj5mbh6a> <!-- 编辑器头部 --> <div class="editor-header" data-astro-cid-mj5mbh6a> <div class="editor-title" data-astro-cid-mj5mbh6a> <h2 data-astro-cid-mj5mbh6a>编辑内容</h2> <div class="editor-status" data-astro-cid-mj5mbh6a> <span id="save-status" class="status-indicator" data-astro-cid-mj5mbh6a>未保存</span> </div> </div> <div class="editor-actions" data-astro-cid-mj5mbh6a> <button id="preview-toggle" class="btn btn--secondary" data-astro-cid-mj5mbh6a> <span class="btn-icon" data-astro-cid-mj5mbh6a>👁️</span>
预览
</button> <button id="save-draft" class="btn btn--secondary" data-astro-cid-mj5mbh6a> <span class="btn-icon" data-astro-cid-mj5mbh6a>💾</span>
保存草稿
</button> <button id="publish-content" class="btn btn--primary" data-astro-cid-mj5mbh6a> <span class="btn-icon" data-astro-cid-mj5mbh6a>🚀</span>
发布
</button> </div> </div> <!-- 编辑器主体 --> <div class="editor-body" data-astro-cid-mj5mbh6a> <!-- 元数据面板 --> <div class="metadata-panel" data-astro-cid-mj5mbh6a> <div class="metadata-form" data-astro-cid-mj5mbh6a> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-collection" class="form-label" data-astro-cid-mj5mbh6a>集合</label> <select id="content-collection" class="form-select" value="internet" data-astro-cid-mj5mbh6a> <option value="news" data-astro-cid-mj5mbh6a>📰 动态资讯</option> <option value="logs" data-astro-cid-mj5mbh6a>📔 研究日志</option> <option value="research" data-astro-cid-mj5mbh6a>📊 研究报告</option> <option value="reflections" data-astro-cid-mj5mbh6a>💭 反思记录</option> <option value="economics" data-astro-cid-mj5mbh6a>💰 经济研究</option> <option value="philosophy" data-astro-cid-mj5mbh6a>🤔 哲学研究</option> <option value="internet" data-astro-cid-mj5mbh6a>🌐 互联网研究</option> <option value="ai" data-astro-cid-mj5mbh6a>🤖 AI研究</option> <option value="future" data-astro-cid-mj5mbh6a>🔮 未来研究</option> <option value="products" data-astro-cid-mj5mbh6a>🛠️ 产品发布</option> </select> </div> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-slug" class="form-label" data-astro-cid-mj5mbh6a>URL 标识符</label> <input type="text" id="content-slug" class="form-input" value="platform-economy-analysis" placeholder="url-friendly-slug" pattern="[a-zA-Z0-9_-]+" title="只允许字母、数字、连字符和下划线" data-astro-cid-mj5mbh6a> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-title" class="form-label" data-astro-cid-mj5mbh6a>标题</label> <input type="text" id="content-title" class="form-input" value="平台经济的双刃剑：机遇与挑战并存" placeholder="输入内容标题" data-astro-cid-mj5mbh6a> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-description" class="form-label" data-astro-cid-mj5mbh6a>描述</label> <textarea id="content-description" class="form-textarea" rows="2" placeholder="简短描述内容..." data-astro-cid-mj5mbh6a>深入分析平台经济模式的发展现状、商业逻辑以及对传统经济结构的影响，探讨其带来的机遇与挑战</textarea> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-tags" class="form-label" data-astro-cid-mj5mbh6a>标签</label> <input type="text" id="content-tags" class="form-input" value="平台经济, 数字化转型, 商业模式, 网络效应, 数据经济" placeholder="标签1, 标签2, 标签3" data-astro-cid-mj5mbh6a> <div class="form-help" data-astro-cid-mj5mbh6a>用逗号分隔多个标签</div> </div> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-author" class="form-label" data-astro-cid-mj5mbh6a>作者</label> <input type="text" id="content-author" class="form-input" value="Pennfly" data-astro-cid-mj5mbh6a> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <div class="checkbox-group" data-astro-cid-mj5mbh6a> <label class="checkbox-label" data-astro-cid-mj5mbh6a> <input type="checkbox" id="content-draft" class="checkbox-input" data-astro-cid-mj5mbh6a> <span class="checkbox-text" data-astro-cid-mj5mbh6a>保存为草稿</span> </label> </div> </div> <div class="form-group" data-astro-cid-mj5mbh6a> <div class="checkbox-group" data-astro-cid-mj5mbh6a> <label class="checkbox-label" data-astro-cid-mj5mbh6a> <input type="checkbox" id="content-featured" class="checkbox-input" checked data-astro-cid-mj5mbh6a> <span class="checkbox-text" data-astro-cid-mj5mbh6a>设为特色内容</span> </label> </div> </div> </div> </div> </div> <!-- 编辑器和预览区域 --> <div class="editor-content" data-astro-cid-mj5mbh6a> <div class="editor-tabs" data-astro-cid-mj5mbh6a> <button id="edit-tab" class="tab-button active" data-astro-cid-mj5mbh6a>编辑</button> <button id="preview-tab" class="tab-button" data-astro-cid-mj5mbh6a>预览</button> <button id="split-tab" class="tab-button" data-astro-cid-mj5mbh6a>分屏</button> </div> <div class="editor-panes" data-astro-cid-mj5mbh6a> <!-- 编辑面板 --> <div id="edit-pane" class="editor-pane active" data-astro-cid-mj5mbh6a> <div class="editor-toolbar" data-astro-cid-mj5mbh6a> <div class="toolbar-group" data-astro-cid-mj5mbh6a> <button class="toolbar-btn" data-action="bold" title="粗体" data-astro-cid-mj5mbh6a> <strong data-astro-cid-mj5mbh6a>B</strong> </button> <button class="toolbar-btn" data-action="italic" title="斜体" data-astro-cid-mj5mbh6a> <em data-astro-cid-mj5mbh6a>I</em> </button> <button class="toolbar-btn" data-action="heading" title="标题" data-astro-cid-mj5mbh6a> H </button> <button class="toolbar-btn" data-action="link" title="链接" data-astro-cid-mj5mbh6a> 🔗 </button> <button class="toolbar-btn" data-action="image" title="图片" data-astro-cid-mj5mbh6a> 🖼️ </button> <button class="toolbar-btn" data-action="code" title="代码" data-astro-cid-mj5mbh6a> &lt;/&gt; </button> <button class="toolbar-btn" data-action="list" title="列表" data-astro-cid-mj5mbh6a> 📝 </button> <button class="toolbar-btn" data-action="quote" title="引用" data-astro-cid-mj5mbh6a> 💬 </button> </div> <div class="toolbar-info" data-astro-cid-mj5mbh6a> <span id="word-count" data-astro-cid-mj5mbh6a>0 字</span> <span id="line-count" data-astro-cid-mj5mbh6a>0 行</span> </div> </div> <textarea id="content-editor" class="markdown-editor" placeholder="在这里使用 Markdown 语法编写内容..." data-astro-cid-mj5mbh6a></textarea> </div> <!-- 预览面板 --> <div id="preview-pane" class="preview-pane" data-astro-cid-mj5mbh6a> <div class="preview-content" id="preview-content" data-astro-cid-mj5mbh6a> <div class="preview-loading" data-astro-cid-mj5mbh6a> <div class="loading-spinner" data-astro-cid-mj5mbh6a></div> <span data-astro-cid-mj5mbh6a>生成预览中...</span> </div> </div> </div> </div> </div> </div> <!-- 保存确认对话框 --> <div id="save-dialog" class="dialog-overlay" style="display: none;" data-astro-cid-mj5mbh6a> <div class="dialog" data-astro-cid-mj5mbh6a> <div class="dialog-header" data-astro-cid-mj5mbh6a> <h3 data-astro-cid-mj5mbh6a>保存内容</h3> <button id="close-dialog" class="dialog-close" data-astro-cid-mj5mbh6a>✕</button> </div> <div class="dialog-body" data-astro-cid-mj5mbh6a> <p data-astro-cid-mj5mbh6a>确定要保存这个内容吗？</p> <div class="dialog-info" data-astro-cid-mj5mbh6a> <div data-astro-cid-mj5mbh6a><strong data-astro-cid-mj5mbh6a>标题:</strong> <span id="dialog-title" data-astro-cid-mj5mbh6a></span></div> <div data-astro-cid-mj5mbh6a><strong data-astro-cid-mj5mbh6a>集合:</strong> <span id="dialog-collection" data-astro-cid-mj5mbh6a></span></div> <div data-astro-cid-mj5mbh6a><strong data-astro-cid-mj5mbh6a>状态:</strong> <span id="dialog-status" data-astro-cid-mj5mbh6a></span></div> </div> </div> <div class="dialog-actions" data-astro-cid-mj5mbh6a> <button id="confirm-save" class="btn btn--primary" data-astro-cid-mj5mbh6a>确认保存</button> <button id="cancel-save" class="btn btn--secondary" data-astro-cid-mj5mbh6a>取消</button> </div> </div> </div> </div>  <script type="module" src="/assets/ContentEditor.astro_astro_type_script_index_0_lang.C2fZR_Fh.js"></script> </main>  </main> <!-- 页脚 --> <footer id="footer" class="mt-12 border-t border-gray-200 bg-gray-100 py-8" role="contentinfo" data-astro-cid-sckkx6r4> <div class="container mx-auto px-4 text-center text-gray-600" data-astro-cid-sckkx6r4> <p data-astro-cid-sckkx6r4>&copy; 2025 Pennfly Private Academy. 保留所有权利。</p> </div> </footer> <!-- 可访问性工具已删除 --> <!-- 性能监控已删除 --> <!-- 返回顶部按钮 --> <button id="back-to-top" class="invisible fixed right-6 bottom-6 rounded-full bg-blue-600 p-3 text-white opacity-0 shadow-lg transition-all duration-300 hover:bg-blue-700" aria-label="返回页面顶部" title="返回顶部" data-astro-cid-sckkx6r4> <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true" data-astro-cid-sckkx6r4> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" data-astro-cid-sckkx6r4></path> </svg> </button>  <script type="module">document.addEventListener("DOMContentLoaded",()=>{r(),i();const o=document.getElementById("back-to-top");function t(){window.scrollY>300?o?.classList.add("visible"):o?.classList.remove("visible")}window.addEventListener("scroll",t),o?.addEventListener("click",()=>{window.scrollTo({top:0,behavior:"smooth"})}),document.addEventListener("keydown",e=>{e.key==="Home"&&e.ctrlKey&&(e.preventDefault(),window.scrollTo({top:0,behavior:"smooth"}))}),document.querySelectorAll(".skip-link").forEach(e=>{e.addEventListener("click",n=>{n.preventDefault();const s=e.getAttribute("href")?.substring(1),c=document.getElementById(s||"");c&&(c.focus(),c.scrollIntoView({behavior:"smooth"}))})})});function r(){const o=document.querySelectorAll("img[data-src]");if("IntersectionObserver"in window){const t=new IntersectionObserver(e=>{e.forEach(n=>{if(n.isIntersecting){const s=n.target;s.dataset.src&&(s.src=s.dataset.src),s.classList.remove("lazy-loading"),s.classList.add("lazy-loaded"),t.unobserve(s)}})},{rootMargin:"50px"});o.forEach(e=>{e.classList.add("lazy-loading"),t.observe(e)})}else o.forEach(t=>{const e=t;e.dataset.src&&(e.src=e.dataset.src),e.classList.add("lazy-loaded")})}function i(){["https://fonts.googleapis.com","https://fonts.gstatic.com","https://cdn.jsdelivr.net"].forEach(e=>{const n=document.createElement("link");n.rel="preconnect",n.href=e,n.crossOrigin="anonymous",document.head.appendChild(n)}),window.location.pathname==="/"&&(a("/news"),a("/research"))}function a(o){const t=document.createElement("link");t.rel="prefetch",t.href=o,document.head.appendChild(t)}</script>  </body></html> 