// @ts-check
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'astro/config';
import { resolve } from 'path';
import node from '@astrojs/node';

// 基础 Markdown 插件
import remarkGfm from 'remark-gfm';

// Load environment variables
const isDev = process.env.NODE_ENV === 'development';
const siteUrl = process.env.SITE_URL || 'https://pennfly.com';
const devPort = parseInt(process.env.DEV_PORT || '4321', 10);
const devHost = process.env.DEV_HOST || 'localhost';
const devOpenBrowser = process.env.DEV_OPEN_BROWSER !== 'false';
const buildSourcemap = process.env.BUILD_SOURCEMAP === 'true' || isDev;
const buildAnalyze = process.env.BUILD_ANALYZE === 'true';

// https://astro.build/config
export default defineConfig({
  // 站点配置
  site: siteUrl,
  base: '/',

  // 输出配置 - 服务器端渲染
  output: 'server',
  adapter: node({
    mode: 'standalone',
  }),

  // 安全配置
  security: {
    checkOrigin: true,
  },

  // Markdown 配置 - 简化版本
  markdown: {
    remarkPlugins: [
      remarkGfm, // GitHub Flavored Markdown
    ],
    // 移除了代码高亮功能
  },

  // 构建配置
  build: {
    assets: 'assets',
  },

  // 图片优化配置
  image: {
    domains: ['pennfly.com'],
    // 移除 formats 配置，使用 Astro 默认的图片优化
    // formats: ['webp', 'avif'], // 这个配置在当前 Astro 版本中不支持
    // quality: 85, // 图片质量
    // densities: [1, 2], // 支持高分辨率屏幕
  },

  vite: {
    plugins: [tailwindcss()],

    // 路径别名配置
    resolve: {
      alias: {
        '@': resolve('./src'),
        '@/components': resolve('./src/components'),
        '@/layouts': resolve('./src/layouts'),
        '@/utils': resolve('./src/utils'),
        '@/styles': resolve('./src/styles'),
        '@/content': resolve('./src/content'),
      },
    },

    // 开发服务器配置
    server: {
      port: devPort,
      host: devHost === 'localhost' ? false : true,
      open: devOpenBrowser,
    },

    // 构建优化配置
    build: {
      target: 'es2022',
      minify: 'esbuild', // 使用 esbuild 进行代码压缩和混淆
      cssMinify: true, // CSS 压缩
      sourcemap: buildSourcemap, // 根据环境变量决定是否生成 sourcemap
      reportCompressedSize: true, // 报告压缩后的大小
      chunkSizeWarningLimit: 500, // 降低 chunk 大小警告限制 (KB)
      assetsInlineLimit: 4096, // 内联小于 4KB 的资源
      rollupOptions: {
        output: {
          // 资源文件缓存策略 - 添加哈希用于缓存破坏
          entryFileNames: 'assets/[name].[hash].js',
          chunkFileNames: 'assets/[name].[hash].js',
          assetFileNames: (/** @type {any} */ assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
              return `assets/images/[name].[hash].${ext}`;
            }
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
              return `assets/fonts/[name].[hash].${ext}`;
            }
            if (/\.css$/i.test(assetInfo.name)) {
              return `assets/styles/[name].[hash].${ext}`;
            }
            return `assets/[name].[hash].${ext}`;
          },
          // 简化的代码分割策略
          manualChunks: {
            // 第三方库分割
            'vendor-search': ['fuse.js'],
            'vendor-mermaid': ['mermaid'],
            // 应用代码分割
            admin: [
              './src/components/admin/ContentEditor.astro',
              './src/components/admin/ContentList.astro',
            ],
            tags: ['./src/components/tags/TagList.astro', './src/components/tags/TagSearch.astro'],
          },
          // 压缩选项
          compact: true,
        },
        // 外部化依赖（如果需要）
        external: [],
        // Rollup 插件
        plugins: [],
      },
    },

    // 依赖预构建优化
    optimizeDeps: {
      include: ['tailwindcss'],
      // 排除不需要预构建的依赖
      exclude: [],
    },

    // CSS 配置
    css: {
      // CSS 模块配置
      modules: {
        localsConvention: 'camelCase',
      },
      // PostCSS 配置
      postcss: {},
      // CSS 代码分割已由 Astro 自动处理
      // CSS 压缩由 Astro 自动处理
    },

    // 静态资源处理
    assetsInclude: ['**/*.woff', '**/*.woff2', '**/*.ttf', '**/*.otf'],
  },
});
