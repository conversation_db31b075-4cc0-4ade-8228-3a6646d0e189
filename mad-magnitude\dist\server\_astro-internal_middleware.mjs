import "es-module-lexer";
import "./assets/astro-designed-error-pages.DaJo0AH9.js";
import "kleur/colors";
import "./assets/astro/server.bG6JcD2R.js";
import "clsx";
import "cookie";
import { s as sequence } from "./assets/index.C3-DMWVa.js";
const __vite_import_meta_env__ = { "ASSETS_PREFIX": void 0, "BASE_URL": "/", "DEV": false, "MODE": "production", "PROD": true, "SITE": "https://pennfly.com", "SSR": true };
const defaultConfig = {
  SITE_URL: "https://pennfly.com",
  SITE_TITLE: "Pennfly Private Academy",
  SITE_DESCRIPTION: "个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台",
  NODE_ENV: "development",
  DEBUG: false,
  BUILD_ANALYZE: false,
  BUILD_SOURCEMAP: true,
  DEV_PORT: 4321,
  DEV_HOST: "localhost",
  DEV_OPEN_BROWSER: true,
  CSP_ENABLED: true,
  CSP_REPORT_ONLY: false,
  CORS_ORIGIN: "*",
  CORS_CREDENTIALS: false,
  FEATURE_COMMENTS: false,
  FEATURE_SEARCH: false,
  FEATURE_ANALYTICS: false,
  FEATURE_NEWSLETTER: false
};
function parseBoolean(value, fallback) {
  if (!value) return fallback;
  return value.toLowerCase() === "true";
}
function parseNumber(value, fallback) {
  if (!value) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
}
function getEnvConfig() {
  const env = Object.assign(__vite_import_meta_env__, { NODE: process.env.NODE, NODE_ENV: process.env.NODE_ENV, OS: process.env.OS });
  return {
    SITE_URL: env.SITE_URL || defaultConfig.SITE_URL,
    SITE_TITLE: env.SITE_TITLE || defaultConfig.SITE_TITLE,
    SITE_DESCRIPTION: env.SITE_DESCRIPTION || defaultConfig.SITE_DESCRIPTION,
    NODE_ENV: env.NODE_ENV || defaultConfig.NODE_ENV,
    DEBUG: parseBoolean(env.DEBUG, defaultConfig.DEBUG),
    BUILD_ANALYZE: parseBoolean(env.BUILD_ANALYZE, defaultConfig.BUILD_ANALYZE),
    BUILD_SOURCEMAP: parseBoolean(env.BUILD_SOURCEMAP, defaultConfig.BUILD_SOURCEMAP),
    DEV_PORT: parseNumber(env.DEV_PORT, defaultConfig.DEV_PORT),
    DEV_HOST: env.DEV_HOST || defaultConfig.DEV_HOST,
    DEV_OPEN_BROWSER: parseBoolean(env.DEV_OPEN_BROWSER, defaultConfig.DEV_OPEN_BROWSER),
    CSP_ENABLED: parseBoolean(env.CSP_ENABLED, defaultConfig.CSP_ENABLED),
    CSP_REPORT_ONLY: parseBoolean(env.CSP_REPORT_ONLY, defaultConfig.CSP_REPORT_ONLY),
    CORS_ORIGIN: env.CORS_ORIGIN || defaultConfig.CORS_ORIGIN,
    CORS_CREDENTIALS: parseBoolean(env.CORS_CREDENTIALS, defaultConfig.CORS_CREDENTIALS),
    FEATURE_COMMENTS: parseBoolean(env.FEATURE_COMMENTS, defaultConfig.FEATURE_COMMENTS),
    FEATURE_SEARCH: parseBoolean(env.FEATURE_SEARCH, defaultConfig.FEATURE_SEARCH),
    FEATURE_ANALYTICS: parseBoolean(env.FEATURE_ANALYTICS, defaultConfig.FEATURE_ANALYTICS),
    FEATURE_NEWSLETTER: parseBoolean(env.FEATURE_NEWSLETTER, defaultConfig.FEATURE_NEWSLETTER)
  };
}
function getSecurityHeaders() {
  const config = getEnvConfig();
  const isDev = config.NODE_ENV === "development";
  const headers = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin"
  };
  if (config.CSP_ENABLED) {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      // Allow inline scripts for Astro
      "style-src 'self' 'unsafe-inline'",
      // Allow inline styles for Tailwind
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "child-src 'none'",
      "worker-src 'self'",
      "frame-ancestors 'none'",
      "form-action 'self'",
      "base-uri 'self'",
      "manifest-src 'self'"
    ];
    if (isDev) {
      cspDirectives.push("connect-src 'self' ws: wss:");
    }
    const cspValue = cspDirectives.join("; ");
    if (config.CSP_REPORT_ONLY) {
      headers["Content-Security-Policy-Report-Only"] = cspValue;
    } else {
      headers["Content-Security-Policy"] = cspValue;
    }
  }
  if (!isDev) {
    headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload";
  }
  headers["Permissions-Policy"] = [
    "camera=()",
    "microphone=()",
    "geolocation=()",
    "interest-cohort=()",
    "payment=()",
    "usb=()"
  ].join(", ");
  return headers;
}
class RateLimiter {
  constructor(maxRequests = 100, windowMs = 15 * 60 * 1e3) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }
  requests = /* @__PURE__ */ new Map();
  isAllowed(identifier) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    const requests = this.requests.get(identifier) || [];
    const recentRequests = requests.filter((time) => time > windowStart);
    if (recentRequests.length >= this.maxRequests) {
      return false;
    }
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    return true;
  }
  reset(identifier) {
    this.requests.delete(identifier);
  }
  cleanup() {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter((time) => time > windowStart);
      if (recentRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, recentRequests);
      }
    }
  }
}
const rateLimiter = new RateLimiter();
if (typeof setInterval !== "undefined") {
  setInterval(() => {
    rateLimiter.cleanup();
  }, 5 * 60 * 1e3);
}
function createSecurityMiddleware() {
  return {
    /**
     * Apply security headers to response
     */
    applyHeaders: (response) => {
      const headers = getSecurityHeaders();
      Object.entries(headers).forEach(([key, value]) => {
        if (value && typeof value === "string") {
          response.headers.set(key, value);
        }
      });
      return response;
    },
    /**
     * Validate request rate limiting
     */
    checkRateLimit: (request) => {
      const clientIP = request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown";
      return rateLimiter.isAllowed(clientIP);
    },
    /**
     * Validate request origin
     */
    validateOrigin: (request) => {
      const origin = request.headers.get("origin");
      const referer = request.headers.get("referer");
      const config = getEnvConfig();
      if (!origin && !referer) {
        return false;
      }
      const allowedOrigins = [config.SITE_URL];
      if (config.NODE_ENV === "development") {
        allowedOrigins.push(`http://${config.DEV_HOST}:${config.DEV_PORT}`);
        allowedOrigins.push("http://localhost:4321");
      }
      if (origin) {
        return allowedOrigins.some((allowed) => origin.startsWith(allowed));
      }
      if (referer) {
        return allowedOrigins.some((allowed) => referer.startsWith(allowed));
      }
      return false;
    }
  };
}
function applySecurityHeaders(response) {
  const headers = getSecurityHeaders();
  Object.entries(headers).forEach(([key, value]) => {
    if (value && typeof value === "string") {
      response.headers.set(key, value);
    }
  });
  return response;
}
function validateRequestSecurity(request) {
  const errors = [];
  const config = getEnvConfig();
  const middleware = createSecurityMiddleware();
  if (!middleware.checkRateLimit(request)) {
    errors.push("Rate limit exceeded");
  }
  if (request.method === "POST" && !middleware.validateOrigin(request)) {
    errors.push("Invalid request origin");
  }
  if (request.method === "POST") {
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json") && !contentType.includes("application/x-www-form-urlencoded") && !contentType.includes("multipart/form-data")) {
      errors.push("Invalid content type");
    }
  }
  const userAgent = request.headers.get("user-agent");
  if (!userAgent || userAgent.length < 10) {
    errors.push("Suspicious or missing user agent");
  }
  if (config.NODE_ENV === "production") {
    const requiredHeaders = ["accept", "accept-language"];
    requiredHeaders.forEach((header) => {
      if (!request.headers.get(header)) {
        errors.push(`Missing required header: ${header}`);
      }
    });
  }
  return {
    isValid: errors.length === 0,
    errors
  };
}
const securityMiddleware = async (context, next) => {
  const { request } = context;
  if (request.url.includes("/api/")) {
    const validation = validateRequestSecurity(request);
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          error: "Security validation failed",
          details: validation.errors
        }),
        {
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }
  }
  const response = await next();
  return applySecurityHeaders(response);
};
const loggingMiddleware = async (context, next) => {
  const { request } = context;
  return next();
};
const onRequest$1 = sequence(
  securityMiddleware,
  loggingMiddleware
  // Add other middleware here as needed
);
const onRequest = sequence(
  onRequest$1
);
export {
  onRequest
};
