import { b as createAstro, c as createComponent, m as maybeR<PERSON>Head, d as addAttribute, a as renderTemplate } from "./astro/server.bG6JcD2R.js";
import "kleur/colors";
import "clsx";
import { g as globalTagManager } from "./tagManager.CZWPi9GW.js";
/* empty css                        */
const $$Astro = createAstro("https://pennfly.com");
const $$TagCloud = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$TagCloud;
  const { maxTags = 30, showCount = true, size = "medium", interactive = true } = Astro2.props;
  const tagStats = globalTagManager ? await globalTagManager.getTagStats() : {
    mostPopularTags: []
  };
  const popularTags = tagStats.mostPopularTags.slice(0, maxTags);
  const maxCount = Math.max(...popularTags.map((tag) => tag.count));
  const minCount = Math.min(...popularTags.map((tag) => tag.count));
  function getTagSize(count) {
    const ratio = (count - minCount) / (maxCount - minCount);
    switch (size) {
      case "small":
        return `${0.8 + ratio * 0.6}rem`;
      // 0.8rem - 1.4rem
      case "large":
        return `${1.2 + ratio * 1.2}rem`;
      // 1.2rem - 2.4rem
      default:
        return `${1 + ratio * 0.8}rem`;
    }
  }
  function getTagOpacity(count) {
    const ratio = (count - minCount) / (maxCount - minCount);
    return 0.6 + ratio * 0.4;
  }
  return renderTemplate`${maybeRenderHead()}<div class="tag-cloud"${addAttribute(interactive, "data-interactive")} data-astro-cid-n2s74bzm> <div class="tag-cloud-container" data-astro-cid-n2s74bzm> ${popularTags.map((tag) => renderTemplate`<a${addAttribute(`/tags/${encodeURIComponent(tag.name)}`, "href")} class="tag-item"${addAttribute(`
          font-size: ${getTagSize(tag.count)};
          opacity: ${getTagOpacity(tag.count)};
          color: ${tag.color};
        `, "style")}${addAttribute(tag.count, "data-count")}${addAttribute(tag.category, "data-category")}${addAttribute(`${tag.name} (${tag.count} 篇内容)`, "title")} data-astro-cid-n2s74bzm> ${tag.name} ${showCount && renderTemplate`<span class="tag-count" data-astro-cid-n2s74bzm>(${tag.count})</span>`} </a>`)} </div> </div>  `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagCloud.astro", void 0);
export {
  $$TagCloud as $
};
