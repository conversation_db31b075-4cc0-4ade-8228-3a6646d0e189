import { b as createAstro, c as createComponent, m as maybeRenderHead, d as addAttribute, e as renderScript, a as renderTemplate } from "./astro/server.bG6JcD2R.js";
import "kleur/colors";
import "clsx";
/* empty css                         */
const $$Astro = createAstro("https://pennfly.com");
const $$ContentEditor = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ContentEditor;
  const { initialContent, mode = "create" } = Astro2.props;
  const defaultContent = {
    collection: "news",
    slug: "",
    title: "",
    description: "",
    draft: true,
    featured: false,
    tags: [],
    author: "Pennfly",
    content: "# 标题\n\n在这里开始编写内容..."
  };
  const content = initialContent || defaultContent;
  return renderTemplate`${maybeRenderHead()}<div class="content-editor" data-astro-cid-mj5mbh6a> <!-- 编辑器头部 --> <div class="editor-header" data-astro-cid-mj5mbh6a> <div class="editor-title" data-astro-cid-mj5mbh6a> <h2 data-astro-cid-mj5mbh6a>${mode === "create" ? "创建新内容" : "编辑内容"}</h2> <div class="editor-status" data-astro-cid-mj5mbh6a> <span id="save-status" class="status-indicator" data-astro-cid-mj5mbh6a>未保存</span> </div> </div> <div class="editor-actions" data-astro-cid-mj5mbh6a> <button id="preview-toggle" class="btn btn--secondary" data-astro-cid-mj5mbh6a> <span class="btn-icon" data-astro-cid-mj5mbh6a>👁️</span>
预览
</button> <button id="save-draft" class="btn btn--secondary" data-astro-cid-mj5mbh6a> <span class="btn-icon" data-astro-cid-mj5mbh6a>💾</span>
保存草稿
</button> <button id="publish-content" class="btn btn--primary" data-astro-cid-mj5mbh6a> <span class="btn-icon" data-astro-cid-mj5mbh6a>🚀</span>
发布
</button> </div> </div> <!-- 编辑器主体 --> <div class="editor-body" data-astro-cid-mj5mbh6a> <!-- 元数据面板 --> <div class="metadata-panel" data-astro-cid-mj5mbh6a> <div class="metadata-form" data-astro-cid-mj5mbh6a> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-collection" class="form-label" data-astro-cid-mj5mbh6a>集合</label> <select id="content-collection" class="form-select"${addAttribute(content.collection, "value")} data-astro-cid-mj5mbh6a> <option value="news" data-astro-cid-mj5mbh6a>📰 动态资讯</option> <option value="logs" data-astro-cid-mj5mbh6a>📔 研究日志</option> <option value="research" data-astro-cid-mj5mbh6a>📊 研究报告</option> <option value="reflections" data-astro-cid-mj5mbh6a>💭 反思记录</option> <option value="economics" data-astro-cid-mj5mbh6a>💰 经济研究</option> <option value="philosophy" data-astro-cid-mj5mbh6a>🤔 哲学研究</option> <option value="internet" data-astro-cid-mj5mbh6a>🌐 互联网研究</option> <option value="ai" data-astro-cid-mj5mbh6a>🤖 AI研究</option> <option value="future" data-astro-cid-mj5mbh6a>🔮 未来研究</option> <option value="products" data-astro-cid-mj5mbh6a>🛠️ 产品发布</option> </select> </div> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-slug" class="form-label" data-astro-cid-mj5mbh6a>URL 标识符</label> <input type="text" id="content-slug" class="form-input"${addAttribute(content.slug, "value")} placeholder="url-friendly-slug" pattern="[a-zA-Z0-9_-]+" title="只允许字母、数字、连字符和下划线" data-astro-cid-mj5mbh6a> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-title" class="form-label" data-astro-cid-mj5mbh6a>标题</label> <input type="text" id="content-title" class="form-input"${addAttribute(content.title, "value")} placeholder="输入内容标题" data-astro-cid-mj5mbh6a> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-description" class="form-label" data-astro-cid-mj5mbh6a>描述</label> <textarea id="content-description" class="form-textarea" rows="2" placeholder="简短描述内容..." data-astro-cid-mj5mbh6a>${content.description}</textarea> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-tags" class="form-label" data-astro-cid-mj5mbh6a>标签</label> <input type="text" id="content-tags" class="form-input"${addAttribute(content.tags.join(", "), "value")} placeholder="标签1, 标签2, 标签3" data-astro-cid-mj5mbh6a> <div class="form-help" data-astro-cid-mj5mbh6a>用逗号分隔多个标签</div> </div> <div class="form-group" data-astro-cid-mj5mbh6a> <label for="content-author" class="form-label" data-astro-cid-mj5mbh6a>作者</label> <input type="text" id="content-author" class="form-input"${addAttribute(content.author, "value")} data-astro-cid-mj5mbh6a> </div> </div> <div class="form-row" data-astro-cid-mj5mbh6a> <div class="form-group" data-astro-cid-mj5mbh6a> <div class="checkbox-group" data-astro-cid-mj5mbh6a> <label class="checkbox-label" data-astro-cid-mj5mbh6a> <input type="checkbox" id="content-draft" class="checkbox-input"${addAttribute(content.draft, "checked")} data-astro-cid-mj5mbh6a> <span class="checkbox-text" data-astro-cid-mj5mbh6a>保存为草稿</span> </label> </div> </div> <div class="form-group" data-astro-cid-mj5mbh6a> <div class="checkbox-group" data-astro-cid-mj5mbh6a> <label class="checkbox-label" data-astro-cid-mj5mbh6a> <input type="checkbox" id="content-featured" class="checkbox-input"${addAttribute(content.featured, "checked")} data-astro-cid-mj5mbh6a> <span class="checkbox-text" data-astro-cid-mj5mbh6a>设为特色内容</span> </label> </div> </div> </div> </div> </div> <!-- 编辑器和预览区域 --> <div class="editor-content" data-astro-cid-mj5mbh6a> <div class="editor-tabs" data-astro-cid-mj5mbh6a> <button id="edit-tab" class="tab-button active" data-astro-cid-mj5mbh6a>编辑</button> <button id="preview-tab" class="tab-button" data-astro-cid-mj5mbh6a>预览</button> <button id="split-tab" class="tab-button" data-astro-cid-mj5mbh6a>分屏</button> </div> <div class="editor-panes" data-astro-cid-mj5mbh6a> <!-- 编辑面板 --> <div id="edit-pane" class="editor-pane active" data-astro-cid-mj5mbh6a> <div class="editor-toolbar" data-astro-cid-mj5mbh6a> <div class="toolbar-group" data-astro-cid-mj5mbh6a> <button class="toolbar-btn" data-action="bold" title="粗体" data-astro-cid-mj5mbh6a> <strong data-astro-cid-mj5mbh6a>B</strong> </button> <button class="toolbar-btn" data-action="italic" title="斜体" data-astro-cid-mj5mbh6a> <em data-astro-cid-mj5mbh6a>I</em> </button> <button class="toolbar-btn" data-action="heading" title="标题" data-astro-cid-mj5mbh6a> H </button> <button class="toolbar-btn" data-action="link" title="链接" data-astro-cid-mj5mbh6a> 🔗 </button> <button class="toolbar-btn" data-action="image" title="图片" data-astro-cid-mj5mbh6a> 🖼️ </button> <button class="toolbar-btn" data-action="code" title="代码" data-astro-cid-mj5mbh6a> &lt;/&gt; </button> <button class="toolbar-btn" data-action="list" title="列表" data-astro-cid-mj5mbh6a> 📝 </button> <button class="toolbar-btn" data-action="quote" title="引用" data-astro-cid-mj5mbh6a> 💬 </button> </div> <div class="toolbar-info" data-astro-cid-mj5mbh6a> <span id="word-count" data-astro-cid-mj5mbh6a>0 字</span> <span id="line-count" data-astro-cid-mj5mbh6a>0 行</span> </div> </div> <textarea id="content-editor" class="markdown-editor" placeholder="在这里使用 Markdown 语法编写内容..." data-astro-cid-mj5mbh6a>${content.content}</textarea> </div> <!-- 预览面板 --> <div id="preview-pane" class="preview-pane" data-astro-cid-mj5mbh6a> <div class="preview-content" id="preview-content" data-astro-cid-mj5mbh6a> <div class="preview-loading" data-astro-cid-mj5mbh6a> <div class="loading-spinner" data-astro-cid-mj5mbh6a></div> <span data-astro-cid-mj5mbh6a>生成预览中...</span> </div> </div> </div> </div> </div> </div> <!-- 保存确认对话框 --> <div id="save-dialog" class="dialog-overlay" style="display: none;" data-astro-cid-mj5mbh6a> <div class="dialog" data-astro-cid-mj5mbh6a> <div class="dialog-header" data-astro-cid-mj5mbh6a> <h3 data-astro-cid-mj5mbh6a>保存内容</h3> <button id="close-dialog" class="dialog-close" data-astro-cid-mj5mbh6a>✕</button> </div> <div class="dialog-body" data-astro-cid-mj5mbh6a> <p data-astro-cid-mj5mbh6a>确定要保存这个内容吗？</p> <div class="dialog-info" data-astro-cid-mj5mbh6a> <div data-astro-cid-mj5mbh6a><strong data-astro-cid-mj5mbh6a>标题:</strong> <span id="dialog-title" data-astro-cid-mj5mbh6a></span></div> <div data-astro-cid-mj5mbh6a><strong data-astro-cid-mj5mbh6a>集合:</strong> <span id="dialog-collection" data-astro-cid-mj5mbh6a></span></div> <div data-astro-cid-mj5mbh6a><strong data-astro-cid-mj5mbh6a>状态:</strong> <span id="dialog-status" data-astro-cid-mj5mbh6a></span></div> </div> </div> <div class="dialog-actions" data-astro-cid-mj5mbh6a> <button id="confirm-save" class="btn btn--primary" data-astro-cid-mj5mbh6a>确认保存</button> <button id="cancel-save" class="btn btn--secondary" data-astro-cid-mj5mbh6a>取消</button> </div> </div> </div> </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/admin/ContentEditor.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/admin/ContentEditor.astro", void 0);
export {
  $$ContentEditor as $
};
