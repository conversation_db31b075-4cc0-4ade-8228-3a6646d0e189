import { i as renderers, o as createExports } from "./assets/vendor-astro.kctgsZae.js";
import { s as serverEntrypointModule } from "./assets/<EMAIL>";
import { manifest } from "./manifest_C8wER1rZ.mjs";
const serverIslandMap = /* @__PURE__ */ new Map();
;
const _page0 = () => import("./pages/_image.astro.mjs");
const _page1 = () => import("./pages/about.astro.mjs");
const _page2 = () => import("./pages/admin/content/create.astro.mjs");
const _page3 = () => import("./pages/admin/content/edit/_---id_.astro.mjs");
const _page4 = () => import("./pages/admin/content.astro.mjs");
const _page5 = () => import("./pages/admin.astro.mjs");
const _page6 = () => import("./pages/ai.astro.mjs");
const _page7 = () => import("./pages/ai/_---slug_.astro.mjs");
const _page8 = () => import("./pages/api/content/create.astro.mjs");
const _page9 = () => import("./pages/api/content/list.astro.mjs");
const _page10 = () => import("./pages/api/content/search.astro.mjs");
const _page11 = () => import("./pages/api/content/stats.astro.mjs");
const _page12 = () => import("./pages/api/content/_id_.astro.mjs");
const _page13 = () => import("./pages/api/tags/search.astro.mjs");
const _page14 = () => import("./pages/api/tags/stats.astro.mjs");
const _page15 = () => import("./pages/collections.astro.mjs");
const _page16 = () => import("./pages/economics.astro.mjs");
const _page17 = () => import("./pages/economics/_---slug_.astro.mjs");
const _page18 = () => import("./pages/future.astro.mjs");
const _page19 = () => import("./pages/future/_---slug_.astro.mjs");
const _page20 = () => import("./pages/internet.astro.mjs");
const _page21 = () => import("./pages/internet/_---slug_.astro.mjs");
const _page22 = () => import("./pages/logs.astro.mjs");
const _page23 = () => import("./pages/logs/_---slug_.astro.mjs");
const _page24 = () => import("./pages/news.astro.mjs");
const _page25 = () => import("./pages/news/_---slug_.astro.mjs");
const _page26 = () => import("./pages/philosophy.astro.mjs");
const _page27 = () => import("./pages/philosophy/_---slug_.astro.mjs");
const _page28 = () => import("./pages/products.astro.mjs");
const _page29 = () => import("./pages/products/_---slug_.astro.mjs");
const _page30 = () => import("./pages/reflections/list.astro.mjs");
const _page31 = () => import("./pages/reflections.astro.mjs");
const _page32 = () => import("./pages/research/list.astro.mjs");
const _page33 = () => import("./pages/research/_slug_.astro.mjs");
const _page34 = () => import("./pages/research.astro.mjs");
const _page35 = () => import("./pages/rss.xml.astro.mjs");
const _page36 = () => import("./pages/search-index.json.astro.mjs");
const _page37 = () => import("./pages/sitemap.xml.astro.mjs");
const _page38 = () => import("./pages/tags/_tag_.astro.mjs");
const _page39 = () => import("./pages/tags.astro.mjs");
const _page40 = () => import("./pages/tags-simple.astro.mjs");
const _page41 = () => import("./pages/index.astro.mjs");
const pageMap = /* @__PURE__ */ new Map([
  ["node_modules/astro/dist/assets/endpoint/node.js", _page0],
  ["src/pages/about.astro", _page1],
  ["src/pages/admin/content/create.astro", _page2],
  ["src/pages/admin/content/edit/[...id].astro", _page3],
  ["src/pages/admin/content/index.astro", _page4],
  ["src/pages/admin/index.astro", _page5],
  ["src/pages/ai/index.astro", _page6],
  ["src/pages/ai/[...slug].astro", _page7],
  ["src/pages/api/content/create.ts", _page8],
  ["src/pages/api/content/list.ts", _page9],
  ["src/pages/api/content/search.ts", _page10],
  ["src/pages/api/content/stats.ts", _page11],
  ["src/pages/api/content/[id].ts", _page12],
  ["src/pages/api/tags/search.ts", _page13],
  ["src/pages/api/tags/stats.ts", _page14],
  ["src/pages/collections/index.astro", _page15],
  ["src/pages/economics/index.astro", _page16],
  ["src/pages/economics/[...slug].astro", _page17],
  ["src/pages/future/index.astro", _page18],
  ["src/pages/future/[...slug].astro", _page19],
  ["src/pages/internet/index.astro", _page20],
  ["src/pages/internet/[...slug].astro", _page21],
  ["src/pages/logs/index.astro", _page22],
  ["src/pages/logs/[...slug].astro", _page23],
  ["src/pages/news/index.astro", _page24],
  ["src/pages/news/[...slug].astro", _page25],
  ["src/pages/philosophy/index.astro", _page26],
  ["src/pages/philosophy/[...slug].astro", _page27],
  ["src/pages/products/index.astro", _page28],
  ["src/pages/products/[...slug].astro", _page29],
  ["src/pages/reflections/list.astro", _page30],
  ["src/pages/reflections/index.astro", _page31],
  ["src/pages/research/list.astro", _page32],
  ["src/pages/research/[slug].astro", _page33],
  ["src/pages/research/index.astro", _page34],
  ["src/pages/rss.xml.ts", _page35],
  ["src/pages/search-index.json.ts", _page36],
  ["src/pages/sitemap.xml.ts", _page37],
  ["src/pages/tags/[tag].astro", _page38],
  ["src/pages/tags/index.astro", _page39],
  ["src/pages/tags-simple.astro", _page40],
  ["src/pages/index.astro", _page41]
]);
const _manifest = Object.assign(manifest, {
  pageMap,
  serverIslandMap,
  renderers,
  actions: () => import("./_noop-actions.mjs"),
  middleware: () => import("./_astro-internal_middleware.mjs")
});
const _args = {
  "mode": "standalone",
  "client": "file:///D:/Projects/PennflyPrivateAcademy/mad-magnitude/dist/client/",
  "server": "file:///D:/Projects/PennflyPrivateAcademy/mad-magnitude/dist/server/",
  "host": false,
  "port": 4321,
  "assets": "assets",
  "experimentalStaticHeaders": false
};
const _exports = createExports(_manifest, _args);
const handler = _exports["handler"];
const startServer = _exports["startServer"];
const options = _exports["options"];
const _start = "start";
if (Object.prototype.hasOwnProperty.call(serverEntrypointModule, _start)) {
  serverEntrypointModule[_start](_manifest, _args);
}
export {
  handler,
  options,
  pageMap,
  startServer
};
