function calculateReadingTime(content) {
  const wordsPerMinute = 200;
  const wordCount = content.length;
  return Math.ceil(wordCount / wordsPerMinute);
}
function buildSearchIndex(posts) {
  return posts.filter((post) => !post.data.draft).map((post) => ({
    id: post.id,
    title: post.data.title.zh,
    description: post.data.description.zh,
    content: post.body,
    tags: post.data.tags,
    category: post.collection,
    url: `/${post.collection}/${post.slug}`,
    publishDate: post.data.publishDate.toISOString()
  }));
}
export {
  buildSearchIndex as b,
  calculateReadingTime as c
};
