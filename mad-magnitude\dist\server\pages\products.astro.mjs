import { c as createComponent, r as renderComponent, e as renderScript, a as renderTemplate, m as maybeRenderHead, d as addAttribute } from "../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import { g as getCollection } from "../assets/_astro_content.Cyc5QpB4.js";
import { $ as $$Layout } from "../assets/Layout.iGDLAGRN.js";
import { f as formatDate } from "../assets/dateUtils.zN-UvZv5.js";
/* empty css                                */
import { renderers } from "../renderers.mjs";
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const products = await getCollection("products");
  const sortedProducts = products.sort((a, b) => {
    if (a.data.featured && !b.data.featured) return -1;
    if (!a.data.featured && b.data.featured) return 1;
    const dateA = a.data.updateDate || a.data.publishDate;
    const dateB = b.data.updateDate || b.data.publishDate;
    return dateB.getTime() - dateA.getTime();
  });
  const stats = {
    total: products.length,
    featured: products.filter((p) => p.data.featured).length,
    withDemo: products.filter((p) => p.data.demo).length
  };
  const allTags = [...new Set(products.flatMap((p) => p.data.tags))];
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "产品发布中心 - Pennfly Private Academy", "description": "展示研究院开发的各类产品和工具，为用户提供实用的解决方案", "data-astro-cid-ttgomkr6": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-7xl mx-auto" data-astro-cid-ttgomkr6> <!-- 页面标题 --> <header class="mb-12" data-astro-cid-ttgomkr6> <div class="text-center" data-astro-cid-ttgomkr6> <h1 class="text-4xl font-bold text-gray-900 mb-4" data-astro-cid-ttgomkr6>
🚀 产品发布中心
</h1> <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed" data-astro-cid-ttgomkr6>
这里汇集了研究院开发的各类产品和工具，每一个产品都经过精心设计和打磨，
          旨在为用户提供实用、高效的解决方案。
</p> </div> </header> <!-- 统计概览 --> <section class="mb-12" data-astro-cid-ttgomkr6> <div class="grid grid-cols-1 md:grid-cols-3 gap-6" data-astro-cid-ttgomkr6> <div class="bg-white rounded-lg border border-gray-200 p-8 text-center" data-astro-cid-ttgomkr6> <div class="text-4xl font-bold text-blue-600 mb-3" data-astro-cid-ttgomkr6>${stats.total}</div> <div class="text-gray-600" data-astro-cid-ttgomkr6>发布产品</div> </div> <div class="bg-white rounded-lg border border-gray-200 p-8 text-center" data-astro-cid-ttgomkr6> <div class="text-4xl font-bold text-yellow-600 mb-3" data-astro-cid-ttgomkr6>${stats.featured}</div> <div class="text-gray-600" data-astro-cid-ttgomkr6>精选产品</div> </div> <div class="bg-white rounded-lg border border-gray-200 p-8 text-center" data-astro-cid-ttgomkr6> <div class="text-4xl font-bold text-green-600 mb-3" data-astro-cid-ttgomkr6>${stats.withDemo}</div> <div class="text-gray-600" data-astro-cid-ttgomkr6>可在线体验</div> </div> </div> </section> <!-- 搜索和筛选 --> <section class="mb-8" data-astro-cid-ttgomkr6> <div class="bg-white rounded-lg border border-gray-200 p-6" data-astro-cid-ttgomkr6> <div class="flex flex-col md:flex-row gap-4 items-center justify-between" data-astro-cid-ttgomkr6> <!-- 搜索框 --> <div class="flex-1 max-w-md" data-astro-cid-ttgomkr6> <input type="text" id="product-search" placeholder="搜索产品..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" data-astro-cid-ttgomkr6> </div> <!-- 标签筛选 --> <div class="flex flex-wrap gap-2" data-astro-cid-ttgomkr6> <button class="filter-btn active px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-tag="all" data-astro-cid-ttgomkr6>
全部
</button> ${allTags.slice(0, 6).map((tag) => renderTemplate`<button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50"${addAttribute(tag, "data-tag")} data-astro-cid-ttgomkr6> ${tag} </button>`)} </div> </div> </div> </section> <!-- 产品列表 --> <section class="mb-12" data-astro-cid-ttgomkr6> <div class="grid gap-8" data-astro-cid-ttgomkr6> ${sortedProducts.map((product) => {
    const updateDate = product.data.updateDate || product.data.publishDate;
    return renderTemplate`<article class="product-card bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200"${addAttribute(product.data.tags.join(","), "data-tags")} data-astro-cid-ttgomkr6> <div class="p-8" data-astro-cid-ttgomkr6> <div class="flex flex-col lg:flex-row gap-6" data-astro-cid-ttgomkr6> <!-- 产品信息 --> <div class="flex-1" data-astro-cid-ttgomkr6> <div class="flex items-start justify-between mb-4" data-astro-cid-ttgomkr6> <div class="flex-1" data-astro-cid-ttgomkr6> <div class="flex items-center gap-3 mb-3" data-astro-cid-ttgomkr6> <h2 class="text-2xl font-bold text-gray-900" data-astro-cid-ttgomkr6> <a${addAttribute(`/products/${product.slug}`, "href")} class="hover:text-blue-600 transition-colors" data-astro-cid-ttgomkr6> ${product.data.title.zh} </a> </h2> ${product.data.featured && renderTemplate`<span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium" data-astro-cid-ttgomkr6>
⭐ 精选
</span>`} </div> <div class="text-sm text-gray-600 mb-4" data-astro-cid-ttgomkr6> <span data-astro-cid-ttgomkr6>发布于 ${formatDate(product.data.publishDate)}</span> ${product.data.updateDate && renderTemplate`<span class="ml-4" data-astro-cid-ttgomkr6>更新于 ${formatDate(updateDate)}</span>`} </div> </div> </div> <p class="text-gray-700 mb-6 leading-relaxed text-lg" data-astro-cid-ttgomkr6> ${product.data.description.zh} </p> <!-- 标签 --> ${product.data.tags.length > 0 && renderTemplate`<div class="mb-4" data-astro-cid-ttgomkr6> <div class="flex flex-wrap gap-2" data-astro-cid-ttgomkr6> ${product.data.tags.map((tag) => renderTemplate`<span class="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium" data-astro-cid-ttgomkr6> ${tag} </span>`)} </div> </div>`} </div> <!-- 操作按钮 --> <div class="flex flex-col gap-3 lg:w-48" data-astro-cid-ttgomkr6> <a${addAttribute(`/products/${product.slug}`, "href")} class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium" data-astro-cid-ttgomkr6>
📖 了解详情
</a> ${product.data.demo && renderTemplate`<a${addAttribute(product.data.demo, "href")} target="_blank" rel="noopener noreferrer" class="inline-flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium" data-astro-cid-ttgomkr6>
🚀 立即体验
</a>`} </div> </div> </div> </article>`;
  })} </div> </section> <!-- 标签云 --> <section class="mb-12" data-astro-cid-ttgomkr6> <div class="bg-white rounded-lg border border-gray-200 p-8" data-astro-cid-ttgomkr6> <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center" data-astro-cid-ttgomkr6>
🏷️ 产品标签
</h2> <div class="flex flex-wrap justify-center gap-3" data-astro-cid-ttgomkr6> ${allTags.map((tag) => {
    const count = products.filter((p) => p.data.tags.includes(tag)).length;
    const size = count > 2 ? "text-lg" : count > 1 ? "text-base" : "text-sm";
    const weight = count > 2 ? "font-bold" : count > 1 ? "font-semibold" : "font-normal";
    return renderTemplate`<span${addAttribute(`bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 px-4 py-2 rounded-lg ${size} ${weight} hover:from-blue-100 hover:to-purple-100 transition-colors cursor-pointer`, "class")} data-astro-cid-ttgomkr6> ${tag} <span class="text-xs text-gray-500 ml-1" data-astro-cid-ttgomkr6>(${count})</span> </span>`;
  })} </div> </div> </section> <!-- 产品理念 --> <section class="mb-12" data-astro-cid-ttgomkr6> <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8" data-astro-cid-ttgomkr6> <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center" data-astro-cid-ttgomkr6>
💡 产品理念
</h2> <div class="grid md:grid-cols-3 gap-6" data-astro-cid-ttgomkr6> <div class="text-center" data-astro-cid-ttgomkr6> <div class="text-4xl mb-3" data-astro-cid-ttgomkr6>🎯</div> <h3 class="text-lg font-semibold text-gray-900 mb-2" data-astro-cid-ttgomkr6>用户至上</h3> <p class="text-gray-600 text-sm" data-astro-cid-ttgomkr6>
深入理解用户需求，打造真正有价值的产品体验
</p> </div> <div class="text-center" data-astro-cid-ttgomkr6> <div class="text-4xl mb-3" data-astro-cid-ttgomkr6>⚡</div> <h3 class="text-lg font-semibold text-gray-900 mb-2" data-astro-cid-ttgomkr6>简洁高效</h3> <p class="text-gray-600 text-sm" data-astro-cid-ttgomkr6>
追求简洁的设计和高效的功能，让复杂的事情变简单
</p> </div> <div class="text-center" data-astro-cid-ttgomkr6> <div class="text-4xl mb-3" data-astro-cid-ttgomkr6>🌟</div> <h3 class="text-lg font-semibold text-gray-900 mb-2" data-astro-cid-ttgomkr6>持续创新</h3> <p class="text-gray-600 text-sm" data-astro-cid-ttgomkr6>
保持对新技术的敏感度，不断探索和创新
</p> </div> </div> </div> </section> </div> ` })} ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/products/index.astro?astro&type=script&index=0&lang.ts")} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/products/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/products/index.astro";
const $$url = "/products";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
