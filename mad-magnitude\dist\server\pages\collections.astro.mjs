import { c as createComponent, r as renderComponent, a as renderTemplate, m as maybeRenderHead, d as addAttribute } from "../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import { g as getCollection } from "../assets/_astro_content.Cyc5QpB4.js";
import { $ as $$Layout } from "../assets/Layout.iGDLAGRN.js";
import { renderers } from "../renderers.mjs";
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const collections = await getCollection("collections");
  const sortedCollections = collections.sort((a, b) => b.data.updateDate.getTime() - a.data.updateDate.getTime());
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "数字资源" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8 max-w-6xl"> <!-- 页面头部 --> <header class="mb-12"> <h1 class="text-3xl md:text-4xl font-bold mb-4">数字资源收藏</h1> <p class="text-gray-600 max-w-3xl">
精心策划的学术资源库，汇集高质量的研究文献、实用工具和专业数据。为研究者、学习者和思考者提供丰富的知识支撑与灵感源泉。
</p> </header> <!-- 资源分类 --> <section> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"> <a href="/collections?category=books" class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300 text-center"> <div class="mx-auto mb-4"> <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto"> <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path> </svg> </div> </div> <h3 class="text-lg font-semibold mb-2">书籍</h3> <p class="text-gray-600 text-sm">精选的学术著作和专业书籍</p> </a> <a href="/collections?category=tools" class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300 text-center"> <div class="mx-auto mb-4"> <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto"> <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> </svg> </div> </div> <h3 class="text-lg font-semibold mb-2">工具</h3> <p class="text-gray-600 text-sm">实用的研究工具和软件</p> </a> <a href="/collections?category=datasets" class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300 text-center"> <div class="mx-auto mb-4"> <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto"> <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path> </svg> </div> </div> <h3 class="text-lg font-semibold mb-2">数据集</h3> <p class="text-gray-600 text-sm">高质量的研究数据和资料</p> </a> <a href="/collections?category=papers" class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300 text-center"> <div class="mx-auto mb-4"> <div class="bg-amber-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto"> <svg class="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> </div> </div> <h3 class="text-lg font-semibold mb-2">论文</h3> <p class="text-gray-600 text-sm">学术论文和研究报告</p> </a> </div> </section> <!-- 资源列表 --> <section> <h2 class="text-2xl font-bold mb-6">最新资源</h2> <div class="space-y-8"> ${sortedCollections.map((collection) => renderTemplate`<div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300"> <div class="flex flex-col md:flex-row md:items-start gap-6"> <div class="md:w-2/3"> <div class="flex items-center justify-between mb-4"> <div> <h3 class="text-xl font-semibold mb-1">${collection.data.title.zh}</h3> ${collection.data.title.en && renderTemplate`<p class="text-gray-600 italic text-sm mb-2">${collection.data.title.en}</p>`} </div> <span class="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700"> ${collection.data.category} </span> </div> <p class="text-gray-700 mb-4">${collection.data.description.zh}</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-4"> ${collection.data.items.map((item, index) => renderTemplate`<a${addAttribute(item.url, "href")} target="_blank" rel="noopener noreferrer" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"> <h4 class="font-medium text-gray-900 mb-1">${item.name}</h4> <p class="text-sm text-gray-600 mb-2">${item.description}</p> <div class="flex flex-wrap gap-1"> ${item.tags.slice(0, 3).map((tag) => renderTemplate`<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"> ${tag} </span>`)} </div> </a>`)} </div> </div> <div class="md:w-1/3 flex flex-col items-center"> <span class="text-sm text-gray-500 mb-4">
更新于 ${collection.data.updateDate.toLocaleDateString("zh-CN")} </span> <a${addAttribute(collection.data.items[0]?.url, "href")} class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
访问资源
<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path> </svg> </a> </div> </div> </div>`)} </div> </section> </main> ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/collections/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/collections/index.astro";
const $$url = "/collections";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
