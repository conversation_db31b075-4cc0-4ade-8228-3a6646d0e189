---
export const prerender = false;

/**
 * 编辑内容页面
 * 提供内容编辑界面
 */
import ContentEditor from '../../../../components/admin/ContentEditor.astro';
import Layout from '../../../../layouts/Layout.astro';
import { contentManager } from '../../../../utils/contentManager';

export async function getStaticPaths() {
  // 获取所有内容用于静态生成
  const allContent = await contentManager.getAllContent();

  return allContent.map(content => ({
    params: { id: content.id },
    props: { content },
  }));
}

const { id } = Astro.params;
const { content } = Astro.props;

// 如果没有找到内容，返回 404
if (!content) {
  return Astro.redirect('/404');
}

// 页面元数据
const title = `编辑: ${content.title} - Pennfly Private Academy`;
const description = `编辑内容: ${content.title}`;

// 准备编辑器初始数据
const initialContent = {
  id: content.id,
  collection: content.collection,
  slug: content.slug,
  title: content.title,
  description: content.description || '',
  draft: content.draft,
  featured: content.featured,
  tags: content.tags,
  author: content.author,
  content: content.content,
};
---

<Layout title={title} description={description}>
  <main class="edit-content-page">
    <ContentEditor mode="edit" initialContent={initialContent} />
  </main>
</Layout>

<style>
  .edit-content-page {
    height: 100vh;
    background: #f8fafc;
    padding: 1rem;
  }

  @media (max-width: 768px) {
    .edit-content-page {
      padding: 0.5rem;
    }
  }
</style>
