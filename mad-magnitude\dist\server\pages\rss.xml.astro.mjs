import rss from "@astrojs/rss";
import { e as getCollection } from "../assets/utils.CrlRgzpX.js";
import { i } from "../assets/vendor-astro.GLJzaJCN.js";
const GET = async ({ site }) => {
  const [news, logs, economics, philosophy, internet, ai, future, products] = await Promise.all([
    getCollection("news"),
    getCollection("logs"),
    getCollection("economics"),
    getCollection("philosophy"),
    getCollection("internet"),
    getCollection("ai"),
    getCollection("future"),
    getCollection("products")
  ]);
  const allContent = [
    ...news.map((item) => ({ ...item, collection: "news" })),
    ...logs.map((item) => ({ ...item, collection: "logs" })),
    ...economics.map((item) => ({ ...item, collection: "economics" })),
    ...philosophy.map((item) => ({ ...item, collection: "philosophy" })),
    ...internet.map((item) => ({ ...item, collection: "internet" })),
    ...ai.map((item) => ({ ...item, collection: "ai" })),
    ...future.map((item) => ({ ...item, collection: "future" })),
    ...products.map((item) => ({ ...item, collection: "products" }))
  ].filter((item) => !item.data.draft).sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime()).slice(0, 20);
  return rss({
    title: "Pennfly Private Academy",
    description: "跨领域学术研究院 - 经济·哲学·AI·未来研究的深度思考与知识分享",
    site: site || "https://pennfly.com",
    items: allContent.map((item) => ({
      title: item.data.title.zh,
      description: item.data.description.zh,
      link: `/${item.collection}/${item.slug}/`,
      pubDate: item.data.publishDate,
      author: item.data.author || "Pennfly",
      categories: item.data.tags || [],
      customData: `
        <language>zh-CN</language>
        <lastBuildDate>${(/* @__PURE__ */ new Date()).toUTCString()}</lastBuildDate>
        ${item.data.updateDate ? `<atom:updated>${item.data.updateDate.toISOString()}</atom:updated>` : ""}
      `
    })),
    customData: `
      <language>zh-CN</language>
      <managingEditor><EMAIL> (Pennfly)</managingEditor>
      <webMaster><EMAIL> (Pennfly)</webMaster>
      <copyright>Copyright ${(/* @__PURE__ */ new Date()).getFullYear()} Pennfly Private Academy</copyright>
      <category>学术研究</category>
      <category>跨领域研究</category>
      <category>知识分享</category>
      <ttl>60</ttl>
      <image>
        <url>https://pennfly.com/images/logo.png</url>
        <title>Pennfly Private Academy</title>
        <link>https://pennfly.com</link>
        <width>144</width>
        <height>144</height>
      </image>
    `,
    xmlns: {
      atom: "http://www.w3.org/2005/Atom",
      content: "http://purl.org/rss/1.0/modules/content/",
      dc: "http://purl.org/dc/elements/1.1/"
    }
  });
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
