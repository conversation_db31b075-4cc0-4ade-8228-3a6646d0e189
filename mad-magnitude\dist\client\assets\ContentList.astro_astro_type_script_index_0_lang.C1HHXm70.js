class d{currentPage=1;currentFilters={};totalPages=1;constructor(){this.bindEvents(),this.loadContent()}bindEvents(){const t=document.getElementById("apply-filters"),e=document.getElementById("clear-filters"),n=document.getElementById("refresh-list"),s=document.getElementById("create-content");t?.addEventListener("click",()=>this.applyFilters()),e?.addEventListener("click",()=>this.clearFilters()),n?.addEventListener("click",()=>this.loadContent()),s?.addEventListener("click",()=>this.createContent()),document.getElementById("search-input")?.addEventListener("keypress",c=>{c.key==="Enter"&&this.applyFilters()});const r=document.getElementById("prev-page"),i=document.getElementById("next-page");r?.addEventListener("click",()=>this.goToPage(this.currentPage-1)),i?.addEventListener("click",()=>this.goToPage(this.currentPage+1))}async loadContent(t=1){try{const e=new URLSearchParams({page:t.toString(),limit:"20",...this.currentFilters}),n=await fetch(`/api/content/list?${e}`),s=await n.json();n.ok?(this.renderContent(s.content),this.renderPagination(s.pagination),this.updateContentCount(s.pagination.total)):this.showError("加载内容失败: "+s.error)}catch(e){this.showError("网络错误: "+e)}}renderContent(t){const e=document.getElementById("content-table-body");if(e){if(t.length===0){e.innerHTML=`
          <tr>
            <td colspan="6" class="loading-row">
              <span>没有找到内容</span>
            </td>
          </tr>
        `;return}e.innerHTML=t.map(n=>`
        <tr data-id="${n.id}">
          <td class="col-title">
            <div>
              <div class="font-medium text-gray-900">${n.title}</div>
              ${n.description?`<div class="text-sm text-gray-500 mt-1">${n.description}</div>`:""}
            </div>
          </td>
          <td class="col-collection">
            <span class="collection-badge">
              ${this.getCollectionIcon(n.collection)}
              ${this.getCollectionDisplayName(n.collection)}
            </span>
          </td>
          <td class="col-status">
            ${this.renderStatusBadge(n)}
          </td>
          <td class="col-author">${n.author}</td>
          <td class="col-date">
            <div class="text-sm">
              ${this.formatDate(n.updateDate||n.publishDate)}
            </div>
          </td>
          <td class="col-actions">
            <div class="flex gap-1">
              <button onclick="contentList.editContent('${n.id}')" class="btn btn--secondary btn--sm" title="编辑">
                ✏️
              </button>
              <button onclick="contentList.deleteContent('${n.id}')" class="btn btn--danger btn--sm" title="删除">
                🗑️
              </button>
            </div>
          </td>
        </tr>
      `).join("")}}renderStatusBadge(t){return t.featured?'<span class="status-badge status-badge--featured">⭐ 特色</span>':t.draft?'<span class="status-badge status-badge--draft">📝 草稿</span>':'<span class="status-badge status-badge--published">✅ 已发布</span>'}renderPagination(t){const e=document.getElementById("pagination-container"),n=document.getElementById("pagination-info-text"),s=document.getElementById("page-numbers"),o=document.getElementById("prev-page"),r=document.getElementById("next-page");if(!e||!n||!s||!o||!r)return;this.currentPage=t.page,this.totalPages=t.totalPages;const i=(t.page-1)*t.limit+1,c=Math.min(t.page*t.limit,t.total);n.textContent=`显示 ${i}-${c} 条，共 ${t.total} 条`,o.disabled=!t.hasPrev,r.disabled=!t.hasNext;const l=this.generatePageNumbers(t.page,t.totalPages);s.innerHTML=l.map(a=>a==="..."?'<span class="px-2">...</span>':`
          <button 
            class="page-number ${a===t.page?"active":""}"
            onclick="contentList.goToPage(${a})"
          >
            ${a}
          </button>
        `).join(""),e.style.display=t.totalPages>1?"flex":"none"}generatePageNumbers(t,e){const n=[];if(e<=7)for(let s=1;s<=e;s++)n.push(s);else{n.push(1),t>4&&n.push("...");const s=Math.max(2,t-1),o=Math.min(e-1,t+1);for(let r=s;r<=o;r++)n.push(r);t<e-3&&n.push("..."),n.push(e)}return n}updateContentCount(t){const e=document.getElementById("content-count");e&&(e.textContent=`共 ${t} 条内容`)}applyFilters(){const t=document.getElementById("collection-filter"),e=document.getElementById("status-filter"),n=document.getElementById("author-filter"),s=document.getElementById("search-input");this.currentFilters={},t?.value&&(this.currentFilters.collection=t.value),e?.value&&(e.value==="published"?this.currentFilters.draft="false":e.value==="draft"?this.currentFilters.draft="true":e.value==="featured"&&(this.currentFilters.featured="true")),n?.value&&(this.currentFilters.author=n.value),s?.value.trim()&&(this.currentFilters.search=s.value.trim()),this.currentPage=1,this.loadContent(1)}clearFilters(){const t=document.getElementById("collection-filter"),e=document.getElementById("status-filter"),n=document.getElementById("author-filter"),s=document.getElementById("search-input");t&&(t.value=""),e&&(e.value=""),n&&(n.value=""),s&&(s.value=""),this.currentFilters={},this.currentPage=1,this.loadContent(1)}goToPage(t){t<1||t>this.totalPages||(this.currentPage=t,this.loadContent(t))}editContent(t){window.location.href=`/admin/content/edit/${encodeURIComponent(t)}`}async deleteContent(t){if(confirm("确定要删除这个内容吗？此操作不可恢复。"))try{const e=await fetch(`/api/content/${encodeURIComponent(t)}`,{method:"DELETE"});if(e.ok)this.loadContent(this.currentPage);else{const n=await e.json();this.showError("删除失败: "+n.error)}}catch(e){this.showError("网络错误: "+e)}}createContent(){window.location.href="/admin/content/create"}showError(t){alert(t)}formatDate(t){return new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}getCollectionDisplayName(t){return{news:"动态资讯",logs:"研究日志",research:"研究报告",reflections:"反思记录",economics:"经济研究",philosophy:"哲学研究",internet:"互联网研究",ai:"AI研究",future:"未来研究",products:"产品发布"}[t]||t}getCollectionIcon(t){return{news:"📰",logs:"📔",research:"📊",reflections:"💭",economics:"💰",philosophy:"🤔",internet:"🌐",ai:"🤖",future:"🔮",products:"🛠️"}[t]||"📄"}}document.addEventListener("DOMContentLoaded",()=>{new d});
