function formatDate(date) {
  const d = typeof date === "string" ? new Date(date) : date;
  if (isNaN(d.getTime())) {
    return "无效日期";
  }
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  return `${year}年${month}月${day}日`;
}
function groupByMonth(items) {
  const grouped = {};
  items.forEach((item) => {
    const date = new Date(item.data.date || item.data.pubDate || /* @__PURE__ */ new Date());
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
    if (!grouped[monthKey]) {
      grouped[monthKey] = [];
    }
    grouped[monthKey].push(item);
  });
  return grouped;
}
export {
  formatDate as f,
  groupByMonth as g
};
