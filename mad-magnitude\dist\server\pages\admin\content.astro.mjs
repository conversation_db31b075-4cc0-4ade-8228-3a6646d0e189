import { b as createAstro, c as createComponent, m as maybeRenderHead, d as addAttribute, e as renderScript, a as renderTemplate, r as renderComponent } from "../../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import "clsx";
/* empty css                                   */
import { $ as $$Layout } from "../../assets/Layout.iGDLAGRN.js";
import { c as contentManager } from "../../assets/contentManager.6eVsAFe1.js";
import { renderers } from "../../renderers.mjs";
const $$Astro = createAstro("https://pennfly.com");
const $$ContentList = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ContentList;
  const {
    initialContent = [],
    showFilters = true,
    showActions = true,
    compact = false
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`content-list ${compact ? "content-list--compact" : ""}`, "class")} data-astro-cid-m2hrbxta> ${showFilters && renderTemplate`<div class="content-filters" data-astro-cid-m2hrbxta> <div class="filters-row" data-astro-cid-m2hrbxta> <div class="filter-group" data-astro-cid-m2hrbxta> <label for="collection-filter" class="filter-label" data-astro-cid-m2hrbxta>
集合
</label> <select id="collection-filter" class="filter-select" data-astro-cid-m2hrbxta> <option value="" data-astro-cid-m2hrbxta>所有集合</option> <option value="news" data-astro-cid-m2hrbxta>📰 动态资讯</option> <option value="logs" data-astro-cid-m2hrbxta>📔 研究日志</option> <option value="research" data-astro-cid-m2hrbxta>📊 研究报告</option> <option value="reflections" data-astro-cid-m2hrbxta>💭 反思记录</option> <option value="economics" data-astro-cid-m2hrbxta>💰 经济研究</option> <option value="philosophy" data-astro-cid-m2hrbxta>🤔 哲学研究</option> <option value="internet" data-astro-cid-m2hrbxta>🌐 互联网研究</option> <option value="ai" data-astro-cid-m2hrbxta>🤖 AI研究</option> <option value="future" data-astro-cid-m2hrbxta>🔮 未来研究</option> <option value="products" data-astro-cid-m2hrbxta>🛠️ 产品发布</option> </select> </div> <div class="filter-group" data-astro-cid-m2hrbxta> <label for="status-filter" class="filter-label" data-astro-cid-m2hrbxta>
状态
</label> <select id="status-filter" class="filter-select" data-astro-cid-m2hrbxta> <option value="" data-astro-cid-m2hrbxta>所有状态</option> <option value="published" data-astro-cid-m2hrbxta>已发布</option> <option value="draft" data-astro-cid-m2hrbxta>草稿</option> <option value="featured" data-astro-cid-m2hrbxta>特色内容</option> </select> </div> <div class="filter-group" data-astro-cid-m2hrbxta> <label for="author-filter" class="filter-label" data-astro-cid-m2hrbxta>
作者
</label> <select id="author-filter" class="filter-select" data-astro-cid-m2hrbxta> <option value="" data-astro-cid-m2hrbxta>所有作者</option> <option value="Pennfly" data-astro-cid-m2hrbxta>Pennfly</option> </select> </div> <div class="filter-group" data-astro-cid-m2hrbxta> <label for="search-input" class="filter-label" data-astro-cid-m2hrbxta>
搜索
</label> <input type="text" id="search-input" class="filter-input" placeholder="搜索标题、内容..." data-astro-cid-m2hrbxta> </div> <div class="filter-actions" data-astro-cid-m2hrbxta> <button id="apply-filters" class="btn btn--primary" data-astro-cid-m2hrbxta>
筛选
</button> <button id="clear-filters" class="btn btn--secondary" data-astro-cid-m2hrbxta>
清除
</button> </div> </div> </div>`} <div class="content-table-container" data-astro-cid-m2hrbxta> <div class="table-header" data-astro-cid-m2hrbxta> <div class="table-actions" data-astro-cid-m2hrbxta> ${showActions && renderTemplate`<button id="create-content" class="btn btn--success" data-astro-cid-m2hrbxta> <span class="btn-icon" data-astro-cid-m2hrbxta>➕</span>
新建内容
</button>`} <button id="refresh-list" class="btn btn--secondary" data-astro-cid-m2hrbxta> <span class="btn-icon" data-astro-cid-m2hrbxta>🔄</span>
刷新
</button> </div> <div class="table-info" data-astro-cid-m2hrbxta> <span id="content-count" data-astro-cid-m2hrbxta>加载中...</span> </div> </div> <div class="table-wrapper" data-astro-cid-m2hrbxta> <table class="content-table" data-astro-cid-m2hrbxta> <thead data-astro-cid-m2hrbxta> <tr data-astro-cid-m2hrbxta> <th class="col-title" data-astro-cid-m2hrbxta>标题</th> <th class="col-collection" data-astro-cid-m2hrbxta>集合</th> <th class="col-status" data-astro-cid-m2hrbxta>状态</th> <th class="col-author" data-astro-cid-m2hrbxta>作者</th> <th class="col-date" data-astro-cid-m2hrbxta>更新时间</th> ${showActions && renderTemplate`<th class="col-actions" data-astro-cid-m2hrbxta>操作</th>`} </tr> </thead> <tbody id="content-table-body" data-astro-cid-m2hrbxta> <tr data-astro-cid-m2hrbxta> <td colspan="6" class="loading-row" data-astro-cid-m2hrbxta> <div class="loading-spinner" data-astro-cid-m2hrbxta></div> <span data-astro-cid-m2hrbxta>加载内容中...</span> </td> </tr> </tbody> </table> </div> <div class="table-pagination" id="pagination-container" style="display: none;" data-astro-cid-m2hrbxta> <div class="pagination-info" data-astro-cid-m2hrbxta> <span id="pagination-info-text" data-astro-cid-m2hrbxta></span> </div> <div class="pagination-controls" data-astro-cid-m2hrbxta> <button id="prev-page" class="btn btn--secondary" disabled data-astro-cid-m2hrbxta>上一页</button> <span id="page-numbers" data-astro-cid-m2hrbxta></span> <button id="next-page" class="btn btn--secondary" disabled data-astro-cid-m2hrbxta>下一页</button> </div> </div> </div> </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/admin/ContentList.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/admin/ContentList.astro", void 0);
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const stats = contentManager ? await contentManager.getContentStats() : {
    total: 0,
    published: 0,
    drafts: 0,
    featured: 0,
    byCollection: {},
    recentActivity: []
  };
  const title = "内容管理 - Pennfly Private Academy";
  const description = "管理和编辑学院的所有内容";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "description": description, "data-astro-cid-4t7oznc4": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="content-management-page" data-astro-cid-4t7oznc4> <div class="container" data-astro-cid-4t7oznc4> <!-- 页面头部 --> <header class="page-header" data-astro-cid-4t7oznc4> <div class="header-content" data-astro-cid-4t7oznc4> <h1 class="page-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>📝</span>
内容管理
</h1> <p class="page-description" data-astro-cid-4t7oznc4>管理和编辑学院的所有内容，包括文章、研究报告、日志等</p> </div> <div class="header-actions" data-astro-cid-4t7oznc4> <a href="/admin/content/create" class="btn btn--primary" data-astro-cid-4t7oznc4> <span class="btn-icon" data-astro-cid-4t7oznc4>➕</span>
新建内容
</a> <a href="/admin" class="btn btn--secondary" data-astro-cid-4t7oznc4> <span class="btn-icon" data-astro-cid-4t7oznc4>🏠</span>
返回后台
</a> </div> </header> <!-- 统计概览 --> <section class="stats-overview" data-astro-cid-4t7oznc4> <div class="stats-grid" data-astro-cid-4t7oznc4> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>📊</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.total}</div> <div class="stat-label" data-astro-cid-4t7oznc4>总内容数</div> </div> </div> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>✅</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.published}</div> <div class="stat-label" data-astro-cid-4t7oznc4>已发布</div> </div> </div> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>📝</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.drafts}</div> <div class="stat-label" data-astro-cid-4t7oznc4>草稿</div> </div> </div> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>⭐</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.featured}</div> <div class="stat-label" data-astro-cid-4t7oznc4>特色内容</div> </div> </div> </div> </section> <!-- 按集合统计 --> <section class="collection-stats" data-astro-cid-4t7oznc4> <h2 class="section-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>📂</span>
集合统计
</h2> <div class="collection-grid" data-astro-cid-4t7oznc4> ${Object.entries(stats.byCollection).map(([collection, count]) => renderTemplate`<div class="collection-card" data-astro-cid-4t7oznc4> <div class="collection-header" data-astro-cid-4t7oznc4> <span class="collection-icon" data-astro-cid-4t7oznc4> ${collection === "news" && "📰"} ${collection === "logs" && "📔"} ${collection === "research" && "📊"} ${collection === "reflections" && "💭"} ${collection === "economics" && "💰"} ${collection === "philosophy" && "🤔"} ${collection === "internet" && "🌐"} ${collection === "ai" && "🤖"} ${collection === "future" && "🔮"} ${collection === "products" && "🛠️"} </span> <span class="collection-name" data-astro-cid-4t7oznc4> ${collection === "news" && "动态资讯"} ${collection === "logs" && "研究日志"} ${collection === "research" && "研究报告"} ${collection === "reflections" && "反思记录"} ${collection === "economics" && "经济研究"} ${collection === "philosophy" && "哲学研究"} ${collection === "internet" && "互联网研究"} ${collection === "ai" && "AI研究"} ${collection === "future" && "未来研究"} ${collection === "products" && "产品发布"} </span> </div> <div class="collection-count" data-astro-cid-4t7oznc4>${count}</div> </div>`)} </div> </section> <!-- 最近活动 --> <section class="recent-activity" data-astro-cid-4t7oznc4> <h2 class="section-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>🕒</span>
最近活动
</h2> <div class="activity-list" data-astro-cid-4t7oznc4> ${stats.recentActivity.slice(0, 10).map((activity) => renderTemplate`<div class="activity-item" data-astro-cid-4t7oznc4> <div class="activity-icon" data-astro-cid-4t7oznc4>${activity.action === "创建" ? "➕" : "✏️"}</div> <div class="activity-content" data-astro-cid-4t7oznc4> <div class="activity-text" data-astro-cid-4t7oznc4> <span class="activity-action" data-astro-cid-4t7oznc4>${activity.action}</span>
了内容：
<span class="activity-title" data-astro-cid-4t7oznc4>${activity.content}</span> </div> <div class="activity-date" data-astro-cid-4t7oznc4> ${new Date(activity.date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  })} </div> </div> </div>`)} </div> </section> <!-- 内容列表 --> <section class="content-list-section" data-astro-cid-4t7oznc4> <h2 class="section-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>📋</span>
内容列表
</h2> ${renderComponent($$result2, "ContentList", $$ContentList, { "showFilters": true, "showActions": true, "data-astro-cid-4t7oznc4": true })} </section> </div> </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/index.astro";
const $$url = "/admin/content";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
