import { c as createComponent, m as maybeRenderHead, a as renderTemplate, d as addAttribute, b as createAstro, e as renderScript, r as renderComponent } from "../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import "clsx";
import { g as getCollection } from "../assets/_astro_content.Cyc5QpB4.js";
import { f as formatDate } from "../assets/dateUtils.zN-UvZv5.js";
/* empty css                                */
import { $ as $$TagCloud } from "../assets/TagCloud.BLtj07sC.js";
import { $ as $$Layout } from "../assets/Layout.iGDLAGRN.js";
import { renderers } from "../renderers.mjs";
const $$AcademyStats = createComponent(async ($$result, $$props, $$slots) => {
  const newsCount = (await getCollection("news")).filter((item) => !item.data.draft).length;
  const logsCount = (await getCollection("logs")).filter((item) => !item.data.draft).length;
  const researchCount = (await getCollection("research")).filter(
    (item) => !item.data.draft
  ).length;
  const economicsCount = (await getCollection("economics")).filter(
    (item) => !item.data.draft
  ).length;
  const philosophyCount = (await getCollection("philosophy")).filter(
    (item) => !item.data.draft
  ).length;
  const aiCount = (await getCollection("ai")).filter((item) => !item.data.draft).length;
  const totalArticles = newsCount + researchCount + economicsCount + philosophyCount + aiCount;
  const allCollections = await Promise.all([
    getCollection("news"),
    getCollection("research"),
    getCollection("economics"),
    getCollection("philosophy"),
    getCollection("ai"),
    getCollection("logs")
  ]);
  const allTags = /* @__PURE__ */ new Set();
  allCollections.flat().forEach((item) => {
    if (item.data.tags) {
      item.data.tags.forEach((tag) => allTags.add(tag));
    }
  });
  const totalTags = allTags.size;
  const allDates = allCollections.flat().map((item) => item.data.publishDate || item.data.date).filter((date) => date).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
  const lastUpdate = allDates[0];
  const stats = [
    {
      icon: "📚",
      label: "研究文章",
      value: totalArticles,
      description: "深度研究与思考"
    },
    {
      icon: "📔",
      label: "研究日志",
      value: logsCount,
      description: "日常思考记录"
    },
    {
      icon: "🏷️",
      label: "内容标签",
      value: totalTags,
      description: "知识分类体系"
    },
    {
      icon: "🔄",
      label: "最近更新",
      value: lastUpdate ? new Date(lastUpdate).toLocaleDateString("zh-CN") : "暂无",
      description: "持续更新中"
    }
  ];
  return renderTemplate`${maybeRenderHead()}<section class="mb-16"> <div class="rounded-lg bg-gradient-to-r from-slate-50 to-blue-50 p-8"> <h3 class="mb-8 text-center text-2xl font-bold text-slate-800">研究院概况</h3> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4"> ${stats.map((stat) => renderTemplate`<div class="text-center"> <div class="mb-3 text-4xl">${stat.icon}</div> <div class="mb-2 text-2xl font-bold text-slate-800">${stat.value}</div> <div class="mb-1 font-medium text-slate-700">${stat.label}</div> <div class="text-sm text-slate-600">${stat.description}</div> </div>`)} </div> <!-- 研究所活跃度 --> <div class="mt-8 border-t border-slate-200 pt-8"> <h4 class="mb-4 text-center text-lg font-semibold text-slate-800">研究所活跃度</h4> <div class="grid gap-4 md:grid-cols-3 lg:grid-cols-5"> <div class="text-center"> <div class="mb-2 text-2xl">💰</div> <div class="text-sm font-medium text-slate-700">经济研究所</div> <div class="text-xs text-slate-600">${economicsCount} 篇文章</div> </div> <div class="text-center"> <div class="mb-2 text-2xl">🤔</div> <div class="text-sm font-medium text-slate-700">哲学研究所</div> <div class="text-xs text-slate-600">${philosophyCount} 篇文章</div> </div> <div class="text-center"> <div class="mb-2 text-2xl">🌐</div> <div class="text-sm font-medium text-slate-700">互联网研究所</div> <div class="text-xs text-slate-600">0 篇文章</div> </div> <div class="text-center"> <div class="mb-2 text-2xl">🤖</div> <div class="text-sm font-medium text-slate-700">AI研究所</div> <div class="text-xs text-slate-600">${aiCount} 篇文章</div> </div> <div class="text-center"> <div class="mb-2 text-2xl">🔮</div> <div class="text-sm font-medium text-slate-700">未来研究所</div> <div class="text-xs text-slate-600">0 篇文章</div> </div> </div> </div> </div> </section>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/home/<USER>", void 0);
const $$RecentContent = createComponent(async ($$result, $$props, $$slots) => {
  const recentNews = await getCollection("news").then(
    (items) => items.filter((item) => !item.data.draft)
  );
  const recentLogs = await getCollection("logs").then(
    (items) => items.filter((item) => !item.data.draft)
  );
  const recentResearch = await getCollection("research").then(
    (items) => items.filter((item) => !item.data.draft)
  );
  const allContent = [
    ...recentNews.map((item) => ({ ...item, type: "news", collection: "news" })),
    ...recentLogs.map((item) => ({ ...item, type: "logs", collection: "logs" })),
    ...recentResearch.map((item) => ({
      ...item,
      type: "research",
      collection: "research"
    }))
  ].sort((a, b) => {
    const dateA = a.data.publishDate || a.data.date;
    const dateB = b.data.publishDate || b.data.date;
    if (!dateA || !dateB) return 0;
    return new Date(dateB).getTime() - new Date(dateA).getTime();
  }).slice(0, 6);
  const typeIcons = {
    news: "📰",
    logs: "📔",
    research: "🔬"
  };
  const typeNames = {
    news: "动态资讯",
    logs: "研究日志",
    research: "研究文章"
  };
  return renderTemplate`${maybeRenderHead()}<section class="mb-16" data-astro-cid-nos5zfq7> <h3 class="mb-8 text-center text-2xl font-bold text-slate-800" data-astro-cid-nos5zfq7>最新内容</h3> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3" data-astro-cid-nos5zfq7> ${allContent.map((item) => {
    const title = typeof item.data.title === "string" ? item.data.title : item.data.title?.zh || "无标题";
    const description = typeof item.data.description === "string" ? item.data.description : item.data.description?.zh || item.data.summary || "";
    const publishDate = item.data.publishDate || item.data.date;
    const url = `/${item.collection}/${item.slug}`;
    return renderTemplate`<article class="rounded-lg bg-white p-6 shadow-md transition-all hover:-translate-y-1 hover:shadow-lg" data-astro-cid-nos5zfq7> <div class="mb-3 flex items-center justify-between" data-astro-cid-nos5zfq7> <div class="flex items-center" data-astro-cid-nos5zfq7> <span class="mr-2 text-lg" data-astro-cid-nos5zfq7>${typeIcons[item.type]}</span> <span class="text-sm font-medium text-blue-600" data-astro-cid-nos5zfq7> ${typeNames[item.type]} </span> </div> ${publishDate && renderTemplate`<time class="text-sm text-slate-500"${addAttribute(publishDate.toISOString(), "datetime")} data-astro-cid-nos5zfq7> ${formatDate(publishDate)} </time>`} </div> <h4 class="mb-3 line-clamp-2 text-lg font-semibold text-slate-800" data-astro-cid-nos5zfq7> <a${addAttribute(url, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-nos5zfq7> ${title} </a> </h4> ${description && renderTemplate`<p class="mb-4 line-clamp-3 text-slate-600" data-astro-cid-nos5zfq7>${description}</p>`} <div class="flex items-center justify-between" data-astro-cid-nos5zfq7> <div class="flex flex-wrap gap-1" data-astro-cid-nos5zfq7> ${item.data.tags?.slice(0, 2).map((tag) => renderTemplate`<span class="rounded-full bg-slate-100 px-2 py-1 text-xs text-slate-600" data-astro-cid-nos5zfq7> ${tag} </span>`)} </div> <a${addAttribute(url, "href")} class="text-sm font-medium text-blue-600 transition-colors hover:text-blue-800" data-astro-cid-nos5zfq7>
阅读更多 →
</a> </div> </article>`;
  })} </div> <div class="mt-8 text-center" data-astro-cid-nos5zfq7> <a href="/search" class="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700" data-astro-cid-nos5zfq7> <span class="mr-2" data-astro-cid-nos5zfq7>🔍</span>
浏览所有内容
</a> </div> </section> `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/home/<USER>", void 0);
const $$Astro$3 = createAstro("https://pennfly.com");
const $$BookIcon = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$3, $$props, $$slots);
  Astro2.self = $$BookIcon;
  const { class: className = "w-6 h-6" } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<svg${addAttribute(className, "class")} fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path> </svg>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/icons/BookIcon.astro", void 0);
const $$Astro$2 = createAstro("https://pennfly.com");
const $$LightBulbIcon = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$LightBulbIcon;
  const { class: className = "w-6 h-6" } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<svg${addAttribute(className, "class")} fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path> </svg>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/icons/LightBulbIcon.astro", void 0);
const $$Astro$1 = createAstro("https://pennfly.com");
const $$UsersIcon = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$UsersIcon;
  const { class: className = "w-6 h-6" } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<svg${addAttribute(className, "class")} fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path> </svg>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/icons/UsersIcon.astro", void 0);
const $$Astro = createAstro("https://pennfly.com");
const $$TagSearch = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$TagSearch;
  const {
    placeholder = "搜索标签...",
    showCategories = true,
    maxSuggestions = 8,
    size = "medium",
    autoFocus = false
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`tag-search tag-search--${size}`, "class")} data-astro-cid-76dt3kya> <div class="search-container" data-astro-cid-76dt3kya> <div class="search-input-wrapper" data-astro-cid-76dt3kya> <input type="text" class="search-input"${addAttribute(placeholder, "placeholder")} autocomplete="off" spellcheck="false"${addAttribute(autoFocus, "autofocus")}${addAttribute(maxSuggestions, "data-max-suggestions")} data-astro-cid-76dt3kya> <div class="search-icon" data-astro-cid-76dt3kya> <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-astro-cid-76dt3kya> <circle cx="11" cy="11" r="8" data-astro-cid-76dt3kya></circle> <path d="m21 21-4.35-4.35" data-astro-cid-76dt3kya></path> </svg> </div> <button class="clear-button" style="display: none;" title="清除搜索" data-astro-cid-76dt3kya> <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-astro-cid-76dt3kya> <line x1="18" y1="6" x2="6" y2="18" data-astro-cid-76dt3kya></line> <line x1="6" y1="6" x2="18" y2="18" data-astro-cid-76dt3kya></line> </svg> </button> </div> ${showCategories && renderTemplate`<div class="category-filter" data-astro-cid-76dt3kya> <select class="category-select" data-astro-cid-76dt3kya> <option value="all" data-astro-cid-76dt3kya>所有分类</option> <option value="technology" data-astro-cid-76dt3kya>🔬 技术</option> <option value="economics" data-astro-cid-76dt3kya>💰 经济</option> <option value="philosophy" data-astro-cid-76dt3kya>🤔 哲学</option> <option value="society" data-astro-cid-76dt3kya>🏛️ 社会</option> <option value="research" data-astro-cid-76dt3kya>📊 研究</option> <option value="tools" data-astro-cid-76dt3kya>🛠️ 工具</option> <option value="general" data-astro-cid-76dt3kya>📝 通用</option> </select> </div>`} </div> <!-- 搜索建议下拉框 --> <div class="suggestions-dropdown" style="display: none;" data-astro-cid-76dt3kya> <div class="suggestions-header" data-astro-cid-76dt3kya> <span class="suggestions-title" data-astro-cid-76dt3kya>搜索建议</span> <span class="suggestions-count" data-astro-cid-76dt3kya></span> </div> <div class="suggestions-list" data-astro-cid-76dt3kya></div> <div class="suggestions-footer" data-astro-cid-76dt3kya> <button class="view-all-button" data-astro-cid-76dt3kya>查看所有结果</button> </div> </div> <!-- 加载状态 --> <div class="loading-indicator" style="display: none;" data-astro-cid-76dt3kya> <div class="loading-spinner" data-astro-cid-76dt3kya></div> <span class="loading-text" data-astro-cid-76dt3kya>搜索中...</span> </div> <!-- 无结果状态 --> <div class="no-results" style="display: none;" data-astro-cid-76dt3kya> <div class="no-results-icon" data-astro-cid-76dt3kya>🔍</div> <div class="no-results-text" data-astro-cid-76dt3kya>未找到相关标签</div> <div class="no-results-suggestion" data-astro-cid-76dt3kya>尝试使用其他关键词</div> </div> </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagSearch.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagSearch.astro", void 0);
const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "首页 - Pennfly Private Academy", "description": "Pennfly Private Academy - 个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台" }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<section class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 py-20 text-center text-white md:py-32"> <!-- 背景装饰 --> <div class="absolute inset-0 opacity-30"> <div class="h-full w-full bg-repeat" style="background-image: url('data:image/svg+xml;utf8,<svg width=\" 60\" height="\&quot;60\&quot;" viewBox="\&quot;0" 0 60 60\" xmlns="\&quot;http://www.w3.org/2000/svg\&quot;"> <g fill="\&quot;none\&quot;" fill-rule="\&quot;evenodd\&quot;"><g fill="\&quot;%23ffffff\&quot;" fill-opacity="\&quot;0.05\&quot;"><circle cx="\&quot;30\&quot;" cy="\&quot;30\&quot;" r="\&quot;2\&quot;/"></circle></g></g> </div>')">
</div> </section> <div class="relative container mx-auto px-6"> <!-- 主标题区域 --> <div class="mb-12"> <h1 class="mb-4 text-5xl leading-tight font-bold md:text-6xl lg:text-7xl"> <span class="bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
Pennfly
</span> <br> <span class="text-white">Private Academy</span> </h1> <p class="mx-auto mb-6 max-w-3xl text-xl leading-relaxed opacity-90 md:text-2xl">
个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台
</p> <p class="mx-auto max-w-2xl text-lg opacity-75">
致力于深度思考与知识创造，探索学术研究的无限可能
</p> </div> <!-- 快速统计 --> <div class="mb-12 grid grid-cols-2 gap-6 md:grid-cols-4"> <div class="rounded-lg bg-white/10 p-4 backdrop-blur-sm"> <div class="text-2xl font-bold">5+</div> <div class="text-sm opacity-90">研究所</div> </div> <div class="rounded-lg bg-white/10 p-4 backdrop-blur-sm"> <div class="text-2xl font-bold">50+</div> <div class="text-sm opacity-90">研究文章</div> </div> <div class="rounded-lg bg-white/10 p-4 backdrop-blur-sm"> <div class="text-2xl font-bold">100+</div> <div class="text-sm opacity-90">研究日志</div> </div> <div class="rounded-lg bg-white/10 p-4 backdrop-blur-sm"> <div class="text-2xl font-bold">持续</div> <div class="text-sm opacity-90">更新中</div> </div> </div> <!-- 行动按钮 --> <div class="flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6"> <a href="/news" class="group inline-flex items-center gap-2 rounded-lg bg-white px-8 py-4 font-semibold text-blue-600 shadow-lg transition-all hover:-translate-y-1 hover:bg-gray-50 hover:shadow-xl"> <span>📰</span> <span>最新动态</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> <a href="/research" class="group inline-flex items-center gap-2 rounded-lg border-2 border-white/30 bg-white/10 px-8 py-4 font-semibold text-white backdrop-blur-sm transition-all hover:-translate-y-1 hover:bg-white/20 hover:shadow-lg"> <span>🔬</span> <span>研究成果</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> <a href="/admin" class="group inline-flex items-center gap-2 rounded-lg border border-white/30 px-6 py-3 text-sm font-medium text-white/90 transition-all hover:bg-white/10 hover:text-white"> <span>⚙️</span> <span>管理后台</span> </a> </div> </div>  <div class="absolute bottom-0 left-0 w-full"> <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block h-16 w-full"> <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="fill-gray-50"></path> </svg> </div> ` })} <!-- 研究院架构展示 --> <main class="bg-gray-50"> <div class="container mx-auto px-6 py-16"> <!-- 研究院介绍 --> <div class="mb-20 text-center"> <h2 class="mb-8 text-4xl font-bold text-slate-800 md:text-5xl">私人研究院架构</h2> <p class="mx-auto max-w-4xl text-xl leading-relaxed text-slate-600">
Pennfly Private Academy 采用研究院组织结构，设立多个专业研究所和功能中心，
        致力于跨领域学术研究和思想交流，以学术风格展示个人的思考成果和研究兴趣。
</p> </div> <!-- 核心板块 --> <div class="mb-20"> <div class="mb-12 text-center"> <h3 class="mb-4 text-3xl font-bold text-slate-800">核心板块</h3> <p class="mx-auto max-w-2xl text-lg text-slate-600">
记录思考轨迹，分享研究成果，构建知识体系
</p> </div> <div class="grid gap-8 md:grid-cols-2"> <div class="rounded-lg bg-white p-6 shadow-md transition-shadow hover:shadow-lg"> <div class="mb-4 flex items-center"> <span class="mr-4 text-3xl">📰</span> <div> <h4 class="text-xl font-semibold text-slate-800">动态资讯</h4> <p class="text-slate-600">个人研究动向，概述文章为主</p> </div> </div> <p class="text-slate-600">
及时分享研究院的最新动态、研究进展和重要公告，让访问者了解最新的学术思考和发现。
</p> <a href="/news" class="mt-4 inline-block font-medium text-blue-600 hover:text-blue-800">
查看动态 →
</a> </div> <div class="rounded-lg bg-white p-6 shadow-md transition-shadow hover:shadow-lg"> <div class="mb-4 flex items-center"> <span class="mr-4 text-3xl">📔</span> <div> <h4 class="text-xl font-semibold text-slate-800">研究日志</h4> <p class="text-slate-600">个人日常研究记录</p> </div> </div> <p class="text-slate-600">
记录日常的研究思考、学习心得和灵感闪现，展示知识创造和思考演进的真实过程。
</p> <a href="/logs" class="mt-4 inline-block font-medium text-blue-600 hover:text-blue-800">
查看日志 →
</a> </div> </div> </div> </div> </main> <!-- 专业研究所 --> <div class="mb-20"> <div class="mb-12 text-center"> <h3 class="mb-4 text-3xl font-bold text-slate-800">专业研究所</h3> <p class="mx-auto max-w-2xl text-lg text-slate-600">
多元化的研究领域，深入探索各个学科的前沿思考
</p> </div> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"> <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-amber-50 to-orange-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl"> <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-amber-200/30 to-orange-200/30"></div> <div class="relative"> <div class="mb-6 flex items-center"> <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-amber-500 to-orange-500 text-2xl text-white shadow-lg">
💰
</div> <div> <h4 class="text-xl font-bold text-slate-800">经济研究所</h4> <p class="text-sm text-slate-600">Economics Institute</p> </div> </div> <p class="mb-6 leading-relaxed text-slate-600">
深入分析市场趋势、政策影响与经济理论，提供独特的经济学视角和前瞻性思考。
</p> <a href="/economics" class="group inline-flex items-center gap-2 font-semibold text-amber-600 transition-all hover:text-amber-700"> <span>进入研究所</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> </div> </div> <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-50 to-indigo-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl"> <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-purple-200/30 to-indigo-200/30"></div> <div class="relative"> <div class="mb-6 flex items-center"> <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-500 text-2xl text-white shadow-lg">
🤔
</div> <div> <h4 class="text-xl font-bold text-slate-800">哲学研究所</h4> <p class="text-sm text-slate-600">Philosophy Institute</p> </div> </div> <p class="mb-6 leading-relaxed text-slate-600">
探索思想的深度与广度，进行哲学理论研究和现实问题的哲学思辨。
</p> <a href="/philosophy" class="group inline-flex items-center gap-2 font-semibold text-purple-600 transition-all hover:text-purple-700"> <span>进入研究所</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> </div> </div> <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-cyan-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl"> <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-blue-200/30 to-cyan-200/30"></div> <div class="relative"> <div class="mb-6 flex items-center"> <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-500 text-2xl text-white shadow-lg">
🌐
</div> <div> <h4 class="text-xl font-bold text-slate-800">互联网研究所</h4> <p class="text-sm text-slate-600">Internet Institute</p> </div> </div> <p class="mb-6 leading-relaxed text-slate-600">
关注互联网行业发展趋势，分析技术变革对社会的深远影响。
</p> <a href="/internet" class="group inline-flex items-center gap-2 font-semibold text-blue-600 transition-all hover:text-blue-700"> <span>进入研究所</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> </div> </div> <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-50 to-emerald-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl"> <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-green-200/30 to-emerald-200/30"></div> <div class="relative"> <div class="mb-6 flex items-center"> <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-green-500 to-emerald-500 text-2xl text-white shadow-lg">
🤖
</div> <div> <h4 class="text-xl font-bold text-slate-800">人工智能研究所</h4> <p class="text-sm text-slate-600">AI Institute</p> </div> </div> <p class="mb-6 leading-relaxed text-slate-600">
深入研究AI技术发展，探索人工智能的应用前景与社会意义。
</p> <a href="/ai" class="group inline-flex items-center gap-2 font-semibold text-green-600 transition-all hover:text-green-700"> <span>进入研究所</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> </div> </div> <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-rose-50 to-pink-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl"> <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-rose-200/30 to-pink-200/30"></div> <div class="relative"> <div class="mb-6 flex items-center"> <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-rose-500 to-pink-500 text-2xl text-white shadow-lg">
🔮
</div> <div> <h4 class="text-xl font-bold text-slate-800">未来研究所</h4> <p class="text-sm text-slate-600">Future Institute</p> </div> </div> <p class="mb-6 leading-relaxed text-slate-600">
前瞻性思考与趋势预判，探索未来社会发展的多种可能性。
</p> <a href="/future" class="group inline-flex items-center gap-2 font-semibold text-rose-600 transition-all hover:text-rose-700"> <span>进入研究所</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> </div> </div> <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-slate-50 to-gray-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl"> <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-slate-200/30 to-gray-200/30"></div> <div class="relative"> <div class="mb-6 flex items-center"> <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-slate-600 to-gray-600 text-2xl text-white shadow-lg">
🛠️
</div> <div> <h4 class="text-xl font-bold text-slate-800">应用开发中心</h4> <p class="text-sm text-slate-600">Development Center</p> </div> </div> <p class="mb-6 leading-relaxed text-slate-600">
将理论转化为实践，展示创意产品的设计思路与开发过程。
</p> <a href="/products" class="group inline-flex items-center gap-2 font-semibold text-slate-600 transition-all hover:text-slate-700"> <span>进入中心</span> <span class="transition-transform group-hover:translate-x-1">→</span> </a> </div> </div> </div> </div> <!-- 研究院统计 --> ${renderComponent($$result, "AcademyStats", $$AcademyStats, {})} <!-- 最新内容 --> ${renderComponent($$result, "RecentContent", $$RecentContent, {})} <!-- 标签云展示 --> <div class="mb-16"> <h3 class="mb-8 text-center text-2xl font-bold text-slate-800">热门标签</h3> <div class="mb-6"> ${renderComponent($$result, "TagSearch", $$TagSearch, { "placeholder": "搜索感兴趣的标签...", "showCategories": true, "size": "medium" })} </div> ${renderComponent($$result, "TagCloud", $$TagCloud, { "maxTags": 40, "showCount": true, "size": "medium", "interactive": true })} <div class="mt-6 text-center"> <a href="/tags" class="inline-flex items-center gap-2 font-medium text-blue-600 hover:text-blue-800"> <span>查看所有标签</span> <span>→</span> </a> </div> </div> <!-- 特色功能 --> <div class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 p-12 text-white"> <!-- 背景装饰 --> <div class="absolute inset-0 opacity-30"> <div class="h-full w-full bg-repeat" style="background-image: url('data:image/svg+xml;utf8,<svg width=\" 40\" height="\&quot;40\&quot;" viewBox="\&quot;0" 0 40 40\" xmlns="\&quot;http://www.w3.org/2000/svg\&quot;"> <g fill="\&quot;%23ffffff\&quot;" fill-opacity="\&quot;0.05\&quot;"><path d="\&quot;M20" 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z\"></path></g> </div>')">
</div> </div> <div class="relative"> <div class="mb-12 text-center"> <h3 class="mb-4 text-3xl font-bold">研究院特色</h3> <p class="mx-auto max-w-2xl text-lg opacity-90">
以学术严谨性为基础，融合个人独特视角，构建跨领域知识体系
</p> </div> <div class="grid gap-8 md:grid-cols-3"> <div class="group text-center"> <div class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-white/10 backdrop-blur-sm transition-all group-hover:scale-110 group-hover:bg-white/20"> ${renderComponent($$result, "BookIcon", $$BookIcon, { "class": "h-10 w-10 text-white" })} </div> <h4 class="mb-4 text-xl font-bold">学术风格</h4> <p class="leading-relaxed opacity-90">
保持专业性但不失可读性，体现学术研究的严谨与深度，让复杂思想变得易于理解
</p> </div> <div class="group text-center"> <div class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-white/10 backdrop-blur-sm transition-all group-hover:scale-110 group-hover:bg-white/20"> ${renderComponent($$result, "LightBulbIcon", $$LightBulbIcon, { "class": "h-10 w-10 text-white" })} </div> <h4 class="mb-4 text-xl font-bold">个人视角</h4> <p class="leading-relaxed opacity-90">
体现独特的思考过程和观点，展示知识创造的真实轨迹，记录思想演进的完整过程
</p> </div> <div class="group text-center"> <div class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-white/10 backdrop-blur-sm transition-all group-hover:scale-110 group-hover:bg-white/20"> ${renderComponent($$result, "UsersIcon", $$UsersIcon, { "class": "h-10 w-10 text-white" })} </div> <h4 class="mb-4 text-xl font-bold">跨领域整合</h4> <p class="leading-relaxed opacity-90">
打破学科边界，探索不同领域间的关联和交叉点，构建综合性的知识框架
</p> </div> </div> </div> <!-- 页脚 --> <footer class="mt-12 border-t border-gray-200 bg-gray-100 py-8"> <div class="container mx-auto px-4 text-center text-gray-600"> <p>&copy; ${(/* @__PURE__ */ new Date()).getFullYear()} Pennfly Private Academy. 保留所有权利。</p> </div> </footer>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/index.astro";
const $$url = "";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
