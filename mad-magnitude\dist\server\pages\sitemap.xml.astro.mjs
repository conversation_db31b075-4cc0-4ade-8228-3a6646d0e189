import { e as getCollection } from "../assets/utils.CrlRgzpX.js";
import { i } from "../assets/vendor-astro.GLJzaJCN.js";
const staticPages = [
  "",
  "news",
  "logs",
  "research",
  "economics",
  "philosophy",
  "internet",
  "ai",
  "future",
  "products",
  "tags",
  "search",
  "admin"
];
const GET = async ({ site }) => {
  const siteUrl = site?.toString() || "https://pennfly.com";
  const [news, logs, economics, philosophy, internet, ai, future, products] = await Promise.all([
    getCollection("news"),
    getCollection("logs"),
    getCollection("economics"),
    getCollection("philosophy"),
    getCollection("internet"),
    getCollection("ai"),
    getCollection("future"),
    getCollection("products")
  ]);
  const allContent = [
    ...news.map((item) => ({ ...item, collection: "news" })),
    ...logs.map((item) => ({ ...item, collection: "logs" })),
    ...economics.map((item) => ({ ...item, collection: "economics" })),
    ...philosophy.map((item) => ({ ...item, collection: "philosophy" })),
    ...internet.map((item) => ({ ...item, collection: "internet" })),
    ...ai.map((item) => ({ ...item, collection: "ai" })),
    ...future.map((item) => ({ ...item, collection: "future" })),
    ...products.map((item) => ({ ...item, collection: "products" }))
  ];
  const publishedContent = allContent.filter((item) => !item.data.draft);
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
  
  <!-- 静态页面 -->
  ${staticPages.map((page2) => {
    const url = page2 === "" ? siteUrl : `${siteUrl}${page2}/`;
    const priority = page2 === "" ? "1.0" : "0.8";
    const changefreq = page2 === "" ? "daily" : "weekly";
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
    <mobile:mobile/>
  </url>`;
  }).join("\n")}

  <!-- 内容页面 -->
  ${publishedContent.map((item) => {
    const url = `${siteUrl}${item.collection}/${item.slug}/`;
    const lastmod = item.data.updateDate || item.data.publishDate;
    const priority = item.data.featured ? "0.9" : "0.7";
    const changefreq = item.collection === "news" ? "weekly" : "monthly";
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod.toISOString().split("T")[0]}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
    <mobile:mobile/>
  </url>`;
  }).join("\n")}

  <!-- 标签页面 -->
  ${Array.from(new Set(publishedContent.flatMap((item) => item.data.tags || []))).map((tag) => {
    const url = `${siteUrl}tags/${encodeURIComponent(tag)}/`;
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
    <mobile:mobile/>
  </url>`;
  }).join("\n")}

</urlset>`;
  return new Response(xml, {
    headers: {
      "Content-Type": "application/xml; charset=utf-8",
      "Cache-Control": "public, max-age=3600"
      // 缓存1小时
    }
  });
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
