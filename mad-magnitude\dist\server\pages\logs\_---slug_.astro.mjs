import { b as createAstro, c as createComponent, r as renderComponent, a as renderTemplate, m as maybeRenderHead, d as addAttribute } from "../../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import { g as getCollection } from "../../assets/_astro_content.Cyc5QpB4.js";
import { $ as $$Layout } from "../../assets/Layout.iGDLAGRN.js";
import { f as formatDate } from "../../assets/dateUtils.zN-UvZv5.js";
/* empty css                                    */
import { renderers } from "../../renderers.mjs";
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
async function getStaticPaths() {
  const logEntries = await getCollection("logs");
  return logEntries.map((entry) => ({
    params: { slug: entry.slug },
    props: { entry }
  }));
}
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { entry } = Astro2.props;
  if (!entry) {
    return Astro2.redirect("/404");
  }
  const { Content } = await entry.render();
  const allLogs = await getCollection("logs", ({ data }) => !data.draft);
  const relatedLogs = allLogs.filter(
    (log) => log.slug !== entry.slug && (log.data.mood === entry.data.mood || entry.data.relatedInstitute && log.data.relatedInstitute && entry.data.relatedInstitute.some(
      (institute) => log.data.relatedInstitute?.includes(institute)
    ))
  ).sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime()).slice(0, 3);
  const breadcrumbs = [
    { label: "首页", href: "/" },
    { label: "研究日志", href: "/logs" },
    { label: entry.data.title, href: `/logs/${entry.slug}` }
  ];
  const moodConfig = {
    thoughtful: { icon: "🤔", label: "深思", color: "blue" },
    critical: { icon: "🧐", label: "批判", color: "red" },
    optimistic: { icon: "😊", label: "乐观", color: "green" },
    analytical: { icon: "🔍", label: "分析", color: "purple" }
  };
  const currentMood = entry.data.mood ? moodConfig[entry.data.mood] : null;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${entry.data.title} - 研究日志 - Pennfly Private Academy`, "description": `${formatDate(entry.data.date)} 的研究日志记录`, "data-astro-cid-4ig4dub3": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<article class="min-h-screen bg-gray-50" data-astro-cid-4ig4dub3> <!-- 面包屑导航 --> <nav class="border-b border-gray-200 bg-white py-4" data-astro-cid-4ig4dub3> <div class="container mx-auto px-6" data-astro-cid-4ig4dub3> <ol class="flex items-center space-x-2 text-sm text-gray-600" data-astro-cid-4ig4dub3> ${breadcrumbs.map((crumb, index) => renderTemplate`<li class="flex items-center" data-astro-cid-4ig4dub3> ${index > 0 && renderTemplate`<span class="mr-2 text-gray-400" data-astro-cid-4ig4dub3>/</span>`} ${index === breadcrumbs.length - 1 ? renderTemplate`<span class="font-medium text-gray-800" data-astro-cid-4ig4dub3>${crumb.label}</span>` : renderTemplate`<a${addAttribute(crumb.href, "href")} class="transition-colors hover:text-amber-600" data-astro-cid-4ig4dub3> ${crumb.label} </a>`} </li>`)} </ol> </div> </nav> <!-- 日志头部 --> <header class="bg-white py-12" data-astro-cid-4ig4dub3> <div class="container mx-auto px-6" data-astro-cid-4ig4dub3> <div class="mx-auto max-w-4xl" data-astro-cid-4ig4dub3> <!-- 日期和心情 --> <div class="mb-6 flex items-center space-x-4" data-astro-cid-4ig4dub3> <div class="flex items-center space-x-2 rounded-lg bg-amber-100 px-3 py-1 text-sm font-medium text-amber-800" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>📅</span> <span data-astro-cid-4ig4dub3>${formatDate(entry.data.date)}</span> </div> ${currentMood && renderTemplate`<div${addAttribute(`flex items-center space-x-2 rounded-lg px-3 py-1 text-sm font-medium bg-${currentMood.color}-100 text-${currentMood.color}-800`, "class")} data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>${currentMood.icon}</span> <span data-astro-cid-4ig4dub3>${currentMood.label}</span> </div>`} ${entry.data.relatedInstitute && entry.data.relatedInstitute.length > 0 && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-4ig4dub3> <span class="text-sm text-gray-600" data-astro-cid-4ig4dub3>相关研究所:</span> ${entry.data.relatedInstitute.map((institute) => renderTemplate`<a${addAttribute(`/${institute}`, "href")} class="inline-flex items-center space-x-1 rounded bg-green-100 px-2 py-1 text-xs text-green-700 transition-colors hover:bg-green-200" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3> ${institute === "economics" ? "💰" : institute === "philosophy" ? "🤔" : institute === "internet" ? "🌐" : institute === "ai" ? "🤖" : "🔮"} </span> <span data-astro-cid-4ig4dub3> ${institute === "economics" ? "经济研究所" : institute === "philosophy" ? "哲学研究所" : institute === "internet" ? "互联网研究所" : institute === "ai" ? "AI研究所" : "未来研究所"} </span> </a>`)} </div>`} </div> <!-- 标题 --> <h1 class="mb-6 text-4xl leading-tight font-bold text-gray-800" data-astro-cid-4ig4dub3> ${entry.data.title} </h1> <!-- 元信息 --> <div class="flex flex-wrap items-center gap-6 border-t border-gray-200 pt-6 text-sm text-gray-600" data-astro-cid-4ig4dub3> <div class="flex items-center space-x-2" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>📔</span> <span data-astro-cid-4ig4dub3>研究日志</span> </div> <div class="flex items-center space-x-2" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>📅</span> <span data-astro-cid-4ig4dub3>记录时间: ${formatDate(entry.data.date)}</span> </div> ${entry.data.weather && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>🌤️</span> <span data-astro-cid-4ig4dub3>天气: ${entry.data.weather}</span> </div>`} ${entry.data.location && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>📍</span> <span data-astro-cid-4ig4dub3>地点: ${entry.data.location}</span> </div>`} </div> </div> </div> </header> <!-- 日志内容 --> <main class="py-12" data-astro-cid-4ig4dub3> <div class="container mx-auto px-6" data-astro-cid-4ig4dub3> <div class="mx-auto max-w-4xl" data-astro-cid-4ig4dub3> <div class="prose prose-lg prose-gray max-w-none" data-astro-cid-4ig4dub3> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-4ig4dub3": true })} </div> </div> </div> </main> <!-- 标签 --> ${entry.data.tags && entry.data.tags.length > 0 && renderTemplate`<section class="bg-white py-8" data-astro-cid-4ig4dub3> <div class="container mx-auto px-6" data-astro-cid-4ig4dub3> <div class="mx-auto max-w-4xl" data-astro-cid-4ig4dub3> <h3 class="mb-4 text-lg font-semibold text-gray-800" data-astro-cid-4ig4dub3>相关标签</h3> <div class="flex flex-wrap gap-2" data-astro-cid-4ig4dub3> ${entry.data.tags.map((tag) => renderTemplate`<span class="cursor-pointer rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200" data-astro-cid-4ig4dub3>
#${tag} </span>`)} </div> </div> </div> </section>`} <!-- 相关日志 --> ${relatedLogs.length > 0 && renderTemplate`<section class="bg-gray-50 py-12" data-astro-cid-4ig4dub3> <div class="container mx-auto px-6" data-astro-cid-4ig4dub3> <div class="mx-auto max-w-4xl" data-astro-cid-4ig4dub3> <h3 class="mb-8 text-2xl font-bold text-gray-800" data-astro-cid-4ig4dub3>相关日志</h3> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3" data-astro-cid-4ig4dub3> ${relatedLogs.map((log) => renderTemplate`<article class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md" data-astro-cid-4ig4dub3> <div class="mb-3 flex items-center justify-between" data-astro-cid-4ig4dub3> <span class="text-xs text-gray-600" data-astro-cid-4ig4dub3>${formatDate(log.data.date)}</span> ${log.data.mood && renderTemplate`<span${addAttribute(`inline-block rounded px-2 py-1 text-xs font-medium ${log.data.mood === "thoughtful" ? "bg-blue-100 text-blue-800" : log.data.mood === "critical" ? "bg-red-100 text-red-800" : log.data.mood === "optimistic" ? "bg-green-100 text-green-800" : "bg-purple-100 text-purple-800"}`, "class")} data-astro-cid-4ig4dub3> ${log.data.mood === "thoughtful" ? "🤔" : log.data.mood === "critical" ? "🧐" : log.data.mood === "optimistic" ? "😊" : "🔍"} </span>`} </div> <h4 class="mb-2 line-clamp-2 font-semibold text-gray-800" data-astro-cid-4ig4dub3> <a${addAttribute(`/logs/${log.slug}`, "href")} class="transition-colors hover:text-amber-600" data-astro-cid-4ig4dub3> ${log.data.title} </a> </h4> ${log.data.tags && log.data.tags.length > 0 && renderTemplate`<div class="flex flex-wrap gap-1" data-astro-cid-4ig4dub3> ${log.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600" data-astro-cid-4ig4dub3>
#${tag} </span>`)} </div>`} </article>`)} </div> </div> </div> </section>`} <!-- 导航按钮 --> <section class="border-t border-gray-200 bg-white py-8" data-astro-cid-4ig4dub3> <div class="container mx-auto px-6" data-astro-cid-4ig4dub3> <div class="mx-auto flex max-w-4xl items-center justify-between" data-astro-cid-4ig4dub3> <a href="/logs" class="inline-flex items-center space-x-2 rounded-lg bg-gray-600 px-6 py-3 text-white transition-colors hover:bg-gray-700" data-astro-cid-4ig4dub3> <span data-astro-cid-4ig4dub3>←</span> <span data-astro-cid-4ig4dub3>返回日志列表</span> </a> <div class="flex items-center space-x-4" data-astro-cid-4ig4dub3> <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="rounded-lg bg-amber-600 px-6 py-3 text-white transition-colors hover:bg-amber-700" data-astro-cid-4ig4dub3>
回到顶部 ↑
</button> </div> </div> </div> </section> </article> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/logs/[...slug].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/logs/[...slug].astro";
const $$url = "/logs/[...slug]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, getStaticPaths, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
