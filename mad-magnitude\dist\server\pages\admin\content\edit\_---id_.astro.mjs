import { b as createAstro, c as createComponent, r as renderComponent, a as renderTemplate, m as maybeRenderHead } from "../../../../assets/astro/server.bG6JcD2R.js";
import "kleur/colors";
import { $ as $$ContentEditor } from "../../../../assets/ContentEditor.DpgKk6Iw.js";
import { $ as $$Layout } from "../../../../assets/Layout.iGDLAGRN.js";
import { c as contentManager } from "../../../../assets/contentManager.6eVsAFe1.js";
/* empty css                                        */
import { renderers } from "../../../../renderers.mjs";
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
async function getStaticPaths() {
  const allContent = await contentManager.getAllContent();
  return allContent.map((content) => ({
    params: { id: content.id },
    props: { content }
  }));
}
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { id } = Astro2.params;
  const { content } = Astro2.props;
  if (!content) {
    return Astro2.redirect("/404");
  }
  const title = `编辑: ${content.title} - Pennfly Private Academy`;
  const description = `编辑内容: ${content.title}`;
  const initialContent = {
    id: content.id,
    collection: content.collection,
    slug: content.slug,
    title: content.title,
    description: content.description || "",
    draft: content.draft,
    featured: content.featured,
    tags: content.tags,
    author: content.author,
    content: content.content
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "description": description, "data-astro-cid-n3dwskkx": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="edit-content-page" data-astro-cid-n3dwskkx> ${renderComponent($$result2, "ContentEditor", $$ContentEditor, { "mode": "edit", "initialContent": initialContent, "data-astro-cid-n3dwskkx": true })} </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/edit/[...id].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/edit/[...id].astro";
const $$url = "/admin/content/edit/[...id]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, getStaticPaths, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};
