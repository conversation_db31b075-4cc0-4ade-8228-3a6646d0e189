/**
 * 设计令牌 CSS 自定义属性
 * 支持运行时主题切换的 CSS 变量定义
 */

:root {
  /* 颜色令牌 - 亮色主题 */
  --color-primary-50: 239 246 255;
  --color-primary-100: 219 234 254;
  --color-primary-200: 191 219 254;
  --color-primary-300: 147 197 253;
  --color-primary-400: 96 165 250;
  --color-primary-500: 59 130 246;
  --color-primary-600: 37 99 235;
  --color-primary-700: 29 78 216;
  --color-primary-800: 30 64 175;
  --color-primary-900: 30 58 138;
  --color-primary-950: 23 37 84;

  --color-secondary-50: 255 251 235;
  --color-secondary-100: 254 243 199;
  --color-secondary-200: 253 230 138;
  --color-secondary-300: 252 211 77;
  --color-secondary-400: 251 191 36;
  --color-secondary-500: 245 158 11;
  --color-secondary-600: 217 119 6;
  --color-secondary-700: 180 83 9;
  --color-secondary-800: 146 64 14;
  --color-secondary-900: 120 53 15;
  --color-secondary-950: 69 26 3;

  --color-neutral-50: 249 250 251;
  --color-neutral-100: 243 244 246;
  --color-neutral-200: 229 231 235;
  --color-neutral-300: 209 213 219;
  --color-neutral-400: 156 163 175;
  --color-neutral-500: 107 114 128;
  --color-neutral-600: 75 85 99;
  --color-neutral-700: 55 65 81;
  --color-neutral-800: 31 41 55;
  --color-neutral-900: 17 24 39;
  --color-neutral-950: 3 7 18;

  /* 语义颜色 */
  --color-success: 16 185 129;
  --color-warning: 245 158 11;
  --color-error: 239 68 68;
  --color-info: 59 130 246;

  /* 背景颜色 - 亮色主题 */
  --color-bg-primary: 255 255 255;
  --color-bg-secondary: 249 250 251;
  --color-bg-tertiary: 243 244 246;

  /* 文本颜色 - 亮色主题 */
  --color-text-primary: 17 24 39;
  --color-text-secondary: 55 65 81;
  --color-text-tertiary: 107 114 128;
  --color-text-inverse: 255 255 255;

  /* 边框颜色 - 亮色主题 */
  --color-border-primary: 229 231 235;
  --color-border-secondary: 209 213 219;
  --color-border-focus: 59 130 246;

  /* 字体令牌 */
  --font-sans:
    'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', system-ui,
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-serif:
    'Crimson Text', 'Source Serif Pro', 'Noto Serif SC', Georgia, 'Times New Roman', serif;
  --font-mono:
    'JetBrains Mono', 'Fira Code', 'Source Code Pro', Consolas, Monaco, 'Courier New', monospace;
  /* 数学字体已移除 */

  /* 间距令牌 */
  --spacing-xs: 0.25rem; /* 4px */
  --spacing-sm: 0.5rem; /* 8px */
  --spacing-md: 1rem; /* 16px */
  --spacing-lg: 1.5rem; /* 24px */
  --spacing-xl: 2rem; /* 32px */
  --spacing-2xl: 3rem; /* 48px */

  /* 圆角令牌 */
  --radius-sm: 0.125rem; /* 2px */
  --radius-md: 0.25rem; /* 4px */
  --radius-lg: 0.5rem; /* 8px */
  --radius-xl: 0.75rem; /* 12px */
  --radius-2xl: 1rem; /* 16px */

  /* 阴影令牌 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 动画令牌 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* 暗色主题 */
[data-theme='dark'] {
  /* 背景颜色 - 暗色主题 */
  --color-bg-primary: 17 24 39;
  --color-bg-secondary: 31 41 55;
  --color-bg-tertiary: 55 65 81;

  /* 文本颜色 - 暗色主题 */
  --color-text-primary: 249 250 251;
  --color-text-secondary: 229 231 235;
  --color-text-tertiary: 156 163 175;
  --color-text-inverse: 17 24 39;

  /* 边框颜色 - 暗色主题 */
  --color-border-primary: 55 65 81;
  --color-border-secondary: 75 85 99;
  --color-border-focus: 96 165 250;

  /* 阴影令牌 - 暗色主题 (更深的阴影) */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}

/* 高对比度主题 */
[data-theme='high-contrast'] {
  --color-text-primary: 0 0 0;
  --color-text-secondary: 0 0 0;
  --color-bg-primary: 255 255 255;
  --color-bg-secondary: 255 255 255;
  --color-border-primary: 0 0 0;
  --color-border-secondary: 0 0 0;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-fast: 0ms;
    --duration-normal: 0ms;
    --duration-slow: 0ms;
  }
}

/* 工具类 - 使用设计令牌 */
.bg-primary {
  background-color: rgb(var(--color-bg-primary));
}

.bg-secondary {
  background-color: rgb(var(--color-bg-secondary));
}

.bg-tertiary {
  background-color: rgb(var(--color-bg-tertiary));
}

.text-primary {
  color: rgb(var(--color-text-primary));
}

.text-secondary {
  color: rgb(var(--color-text-secondary));
}

.text-tertiary {
  color: rgb(var(--color-text-tertiary));
}

.border-primary {
  border-color: rgb(var(--color-border-primary));
}

.border-secondary {
  border-color: rgb(var(--color-border-secondary));
}

.shadow-token-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-token-md {
  box-shadow: var(--shadow-md);
}

.shadow-token-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-token-xl {
  box-shadow: var(--shadow-xl);
}

.font-sans-token {
  font-family: var(--font-sans);
}

.font-serif-token {
  font-family: var(--font-serif);
}

.font-mono-token {
  font-family: var(--font-mono);
}

.font-math-token {
  font-family: var(--font-math);
}
