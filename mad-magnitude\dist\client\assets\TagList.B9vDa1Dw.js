import { b as createAstro, c as createComponent, m as maybeRenderHead, d as addAttribute, e as renderScript, a as renderTemplate } from "./astro/server.bG6JcD2R.js";
import "kleur/colors";
import "clsx";
/* empty css                              */
const $$Astro = createAstro("https://pennfly.com");
const $$TagList = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$TagList;
  const {
    tags = [],
    variant = "default",
    showCount = false,
    maxTags,
    linkable = true,
    size = "medium"
  } = Astro2.props;
  const displayTags = maxTags ? tags.slice(0, maxTags) : tags;
  const hasMoreTags = maxTags && tags.length > maxTags;
  const remainingCount = hasMoreTags ? tags.length - maxTags : 0;
  function getTagColor(tag) {
    const colors = [
      "#3b82f6",
      "#10b981",
      "#8b5cf6",
      "#f59e0b",
      "#ef4444",
      "#6b7280",
      "#64748b",
      "#06b6d4"
    ];
    let hash = 0;
    for (let i = 0; i < tag.length; i++) {
      hash = tag.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`tag-list tag-list--${variant} tag-list--${size}`, "class")} data-astro-cid-jeqx33lf> ${displayTags.map(
    (tag) => linkable ? renderTemplate`<a${addAttribute(`/tags/${encodeURIComponent(tag)}`, "href")} class="tag-item tag-link"${addAttribute(`--tag-color: ${getTagColor(tag)}`, "style")}${addAttribute(`查看标签"${tag}"的所有内容`, "title")} data-astro-cid-jeqx33lf> <span class="tag-text" data-astro-cid-jeqx33lf>${tag}</span> ${showCount && renderTemplate`<span class="tag-count" data-astro-cid-jeqx33lf>(0)</span>`} </a>` : renderTemplate`<span class="tag-item tag-static"${addAttribute(`--tag-color: ${getTagColor(tag)}`, "style")} data-astro-cid-jeqx33lf> <span class="tag-text" data-astro-cid-jeqx33lf>${tag}</span> ${showCount && renderTemplate`<span class="tag-count" data-astro-cid-jeqx33lf>(0)</span>`} </span>`
  )} ${hasMoreTags && renderTemplate`<span class="tag-item tag-more"${addAttribute(`还有 ${remainingCount} 个标签`, "title")} data-astro-cid-jeqx33lf>
+${remainingCount} </span>`} </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagList.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagList.astro", void 0);
export {
  $$TagList as $
};
