---
export const prerender = false;

import Layout from '@/layouts/Layout.astro';
import { calculateReadingTime } from '@/utils/content';
import { getCollection } from 'astro:content';

export async function getStaticPaths() {
  const reflections = await getCollection('reflections');

  return reflections.map(post => ({
    params: { slug: post.slug },
    props: { post }
  }));
}

export async function getStaticProps({ params }) {
  const reflections = await getCollection('reflections');
  const post = reflections.find(p => p.slug === params.slug);

  if (!post) {
    return {
      props: { post: null }
    };
  }

  const readingTime = calculateReadingTime(post.body);

  return {
    props: {
      post,
      readingTime
    }
  };
}

const { post, readingTime } = Astro.props;
---

<Layout title={post?.data.title.zh || "个人思考"}>
  <main class="container mx-auto px-4 py-8 max-w-4xl">
    {post ? (
      <>
        <article class="prose prose-lg max-w-none">
          <!-- 文章头部 -->
          <header class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">{post.data.title.zh}</h1>
            {post.data.title.en && (
              <p class="text-gray-600 italic mb-6">{post.data.title.en}</p>
            )}

            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
              <span class="flex items-center">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                {post.data.publishDate.toLocaleDateString('zh-CN')}
              </span>

              {post.data.updateDate && (
                <span class="flex items-center">
                  <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  {post.data.updateDate.toLocaleDateString('zh-CN')}
                </span>
              )}

              <span class="flex items-center">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {readingTime} 分钟阅读
              </span>

              {post.data.mood && (
                <span class="flex items-center">
                  <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  {post.data.mood}
                </span>
              )}
            </div>

            <!-- 文章摘要 -->
            <p class="text-gray-700 mb-6">{post.data.description.zh}</p>

            <!-- 标签 -->
            <div class="flex flex-wrap gap-2 mb-8">
              {post.data.tags.map(tag => (
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                  {tag}
                </span>
              ))}
            </div>
          </header>

          <!-- 文章内容 -->
          <div class="prose prose-lg max-w-none">
            {post.body}
          </div>

          <!-- 文章底部 -->
          <footer class="mt-12 pt-6 border-t border-gray-200">
            <p class="text-gray-600 text-sm">
              版权所有 © {post.data.author}。保留所有权利。
            </p>
          </footer>
        </article>
      </>
    ) : (
      <div class="text-center py-12">
        <h1 class="text-2xl font-bold mb-4">文章未找到</h1>
        <p class="text-gray-600">抱歉，您要查找的文章不存在或已被删除。</p>
      </div>
    )}
  </main>
</Layout>
