/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}', './public/**/*.html'],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      // 设计令牌颜色扩展
      colors: {
        // 主色调 - 学术蓝
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        },

        // 辅助色 - 学术金
        secondary: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
          950: '#451a03',
        },

        // 语义颜色
        success: {
          light: '#10b981',
          DEFAULT: '#059669',
          dark: '#047857',
        },
        warning: {
          light: '#f59e0b',
          DEFAULT: '#d97706',
          dark: '#b45309',
        },
        error: {
          light: '#ef4444',
          DEFAULT: '#dc2626',
          dark: '#b91c1c',
        },
        info: {
          light: '#3b82f6',
          DEFAULT: '#2563eb',
          dark: '#1d4ed8',
        },

        // Theme-aware colors using CSS custom properties
        theme: {
          'bg-primary': 'var(--color-background-primary)',
          'bg-secondary': 'var(--color-background-secondary)',
          'bg-tertiary': 'var(--color-background-tertiary)',
          'bg-elevated': 'var(--color-background-elevated)',
          'fg-primary': 'var(--color-foreground-primary)',
          'fg-secondary': 'var(--color-foreground-secondary)',
          'fg-tertiary': 'var(--color-foreground-tertiary)',
          'fg-inverse': 'var(--color-foreground-inverse)',
          'brand-primary': 'var(--color-brand-primary)',
          'brand-secondary': 'var(--color-brand-secondary)',
          'brand-accent': 'var(--color-brand-accent)',
          'semantic-success': 'var(--color-semantic-success)',
          'semantic-warning': 'var(--color-semantic-warning)',
          'semantic-error': 'var(--color-semantic-error)',
          'semantic-info': 'var(--color-semantic-info)',
          'interactive-default': 'var(--color-interactive-default)',
          'interactive-hover': 'var(--color-interactive-hover)',
          'interactive-active': 'var(--color-interactive-active)',
          'interactive-disabled': 'var(--color-interactive-disabled)',
          'interactive-focus': 'var(--color-interactive-focus)',
          'border-default': 'var(--color-border-default)',
          'border-subtle': 'var(--color-border-subtle)',
          'border-strong': 'var(--color-border-strong)',
          'border-interactive': 'var(--color-border-interactive)',
        },
      },

      // 字体系统扩展
      fontFamily: {
        sans: [
          'Inter',
          'PingFang SC',
          'Hiragino Sans GB',
          'Microsoft YaHei',
          'WenQuanYi Micro Hei',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Helvetica Neue',
          'Helvetica',
          'Arial',
          'sans-serif',
        ],
        serif: [
          'Crimson Text',
          'Source Serif Pro',
          'Noto Serif SC',
          'Georgia',
          'Times New Roman',
          'serif',
        ],
        mono: [
          'JetBrains Mono',
          'Fira Code',
          'Source Code Pro',
          'Consolas',
          'Monaco',
          'Courier New',
          'monospace',
        ],
        // 数学字体已移除
      },

      // 字体大小扩展 (包含行高和字母间距)
      fontSize: {
        xs: ['0.75rem', { lineHeight: '1rem', letterSpacing: '0.025em' }],
        sm: ['0.875rem', { lineHeight: '1.25rem', letterSpacing: '0.025em' }],
        base: ['1rem', { lineHeight: '1.5rem', letterSpacing: '0' }],
        lg: ['1.125rem', { lineHeight: '1.75rem', letterSpacing: '-0.025em' }],
        xl: ['1.25rem', { lineHeight: '1.75rem', letterSpacing: '-0.025em' }],
        '2xl': ['1.5rem', { lineHeight: '2rem', letterSpacing: '-0.025em' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem', letterSpacing: '-0.05em' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem', letterSpacing: '-0.05em' }],
        '5xl': ['3rem', { lineHeight: '1', letterSpacing: '-0.05em' }],
        '6xl': ['3.75rem', { lineHeight: '1', letterSpacing: '-0.05em' }],
        '7xl': ['4.5rem', { lineHeight: '1', letterSpacing: '-0.05em' }],
        '8xl': ['6rem', { lineHeight: '1', letterSpacing: '-0.05em' }],
        '9xl': ['8rem', { lineHeight: '1', letterSpacing: '-0.05em' }],
      },

      // 响应式断点优化 - 支持更多设备尺寸
      screens: {
        xs: '475px', // 大型手机
        sm: '640px', // 小型平板
        md: '768px', // 平板
        lg: '1024px', // 小型桌面
        xl: '1280px', // 桌面
        '2xl': '1536px', // 大型桌面
        '3xl': '1920px', // 超大屏幕
        // 特殊断点
        'xs-only': { max: '639px' }, // 仅小屏幕
        'sm-only': { min: '640px', max: '767px' }, // 仅小平板
        'md-only': { min: '768px', max: '1023px' }, // 仅平板
        'lg-only': { min: '1024px', max: '1279px' }, // 仅小桌面
        'xl-only': { min: '1280px', max: '1535px' }, // 仅桌面
        // 设备特定断点
        mobile: { max: '767px' }, // 移动设备
        tablet: { min: '768px', max: '1023px' }, // 平板设备
        desktop: { min: '1024px' }, // 桌面设备
        // 方向断点
        portrait: { raw: '(orientation: portrait)' },
        landscape: { raw: '(orientation: landscape)' },
      },

      // 间距系统扩展
      spacing: {
        0.5: '0.125rem', // 2px
        1.5: '0.375rem', // 6px
        2.5: '0.625rem', // 10px
        3.5: '0.875rem', // 14px
        18: '4.5rem', // 72px
        88: '22rem', // 352px
        100: '25rem', // 400px
        104: '26rem', // 416px
        112: '28rem', // 448px
        128: '32rem', // 512px
      },

      // 圆角系统扩展
      borderRadius: {
        none: '0px',
        sm: '0.125rem', // 2px
        DEFAULT: '0.25rem', // 4px
        md: '0.375rem', // 6px
        lg: '0.5rem', // 8px
        xl: '0.75rem', // 12px
        '2xl': '1rem', // 16px
        '3xl': '1.5rem', // 24px
        full: '9999px', // 完全圆形
      },

      // 阴影系统扩展
      boxShadow: {
        sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
        md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
        xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
        inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
        none: '0 0 #0000',

        // 彩色阴影
        primary: '0 4px 14px 0 rgb(37 99 235 / 0.2)',
        secondary: '0 4px 14px 0 rgb(245 158 11 / 0.2)',
        success: '0 4px 14px 0 rgb(16 185 129 / 0.2)',
        warning: '0 4px 14px 0 rgb(245 158 11 / 0.2)',
        error: '0 4px 14px 0 rgb(239 68 68 / 0.2)',

        // Theme-aware shadows using CSS custom properties
        'theme-sm': 'var(--shadow-sm)',
        'theme-md': 'var(--shadow-md)',
        'theme-lg': 'var(--shadow-lg)',
        'theme-xl': 'var(--shadow-xl)',
        'theme-focus': 'var(--shadow-focus)',
      },

      // 动画时长
      transitionDuration: {
        150: '150ms',
        300: '300ms',
        500: '500ms',
      },

      // 动画缓动函数
      transitionTimingFunction: {
        'ease-in-out-cubic': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'ease-out-cubic': 'cubic-bezier(0, 0, 0.2, 1)',
        'ease-in-cubic': 'cubic-bezier(0.4, 0, 1, 1)',
      },
    },
  },
  plugins: [],
};
